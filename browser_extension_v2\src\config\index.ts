/**
 * ScreenMonitorMCP Browser Extension - Configuration Management
 * 
 * Centralized configuration system with environment-based settings,
 * runtime updates, and validation.
 */

import type { ExtensionConfig, ExtensionFeature, ValidationResult } from '../types';
import { validators } from '../utils/Validator';

// ============================================================================
// Environment Configuration
// ============================================================================

export interface EnvironmentConfig {
  readonly name: string;
  readonly mcpServerUrl: string;
  readonly debugMode: boolean;
  readonly logLevel: 'debug' | 'info' | 'warn' | 'error';
  readonly enabledFeatures: ExtensionFeature[];
  readonly allowedDomains: string[];
  readonly securityLevel: 'low' | 'medium' | 'high';
}

// ============================================================================
// Environment Definitions
// ============================================================================

const DEVELOPMENT_CONFIG: EnvironmentConfig = {
  name: 'development',
  mcpServerUrl: 'http://localhost:7777',
  debugMode: true,
  logLevel: 'debug',
  enabledFeatures: [
    'dom_monitoring',
    'smart_click',
    'text_extraction',
    'event_analysis',
    'form_automation',
    'navigation_tracking'
  ],
  allowedDomains: [
    'localhost',
    '127.0.0.1',
    'file',
    'github.com',
    'stackoverflow.com',
    'developer.mozilla.org'
  ],
  securityLevel: 'medium'
};

const PRODUCTION_CONFIG: EnvironmentConfig = {
  name: 'production',
  mcpServerUrl: 'http://localhost:7777',
  debugMode: false,
  logLevel: 'warn',
  enabledFeatures: [
    'dom_monitoring',
    'smart_click',
    'text_extraction',
    'event_analysis'
  ],
  allowedDomains: [
    'localhost',
    '127.0.0.1',
    'file'
  ],
  securityLevel: 'high'
};

const TEST_CONFIG: EnvironmentConfig = {
  name: 'test',
  mcpServerUrl: 'http://localhost:7778',
  debugMode: true,
  logLevel: 'debug',
  enabledFeatures: [
    'dom_monitoring',
    'smart_click'
  ],
  allowedDomains: [
    'localhost',
    '127.0.0.1',
    'file'
  ],
  securityLevel: 'low'
};

// ============================================================================
// Configuration Manager
// ============================================================================

export class ConfigurationManager {
  private static instance: ConfigurationManager | null = null;
  private currentConfig: ExtensionConfig;
  private environmentConfig: EnvironmentConfig;
  private readonly storageKey = 'extension_config_v2';
  private readonly listeners = new Set<(config: ExtensionConfig) => void>();

  private constructor() {
    this.environmentConfig = this.detectEnvironment();
    this.currentConfig = this.createDefaultConfig();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  /**
   * Initialize configuration from storage
   */
  async initialize(): Promise<void> {
    try {
      const stored = await this.loadFromStorage();
      if (stored) {
        const validation = this.validateConfig(stored);
        if (validation.isValid) {
          this.currentConfig = this.mergeConfigs(this.createDefaultConfig(), stored);
        } else {
          console.warn('Invalid stored configuration, using defaults:', validation.errors);
          await this.saveToStorage();
        }
      } else {
        await this.saveToStorage();
      }

      this.notifyListeners();
    } catch (error) {
      console.error('Failed to initialize configuration:', error);
      throw new Error('Configuration initialization failed');
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): ExtensionConfig {
    return { ...this.currentConfig };
  }

  /**
   * Get environment configuration
   */
  getEnvironmentConfig(): EnvironmentConfig {
    return { ...this.environmentConfig };
  }

  /**
   * Update configuration
   */
  async updateConfig(updates: Partial<ExtensionConfig>): Promise<ValidationResult> {
    try {
      const newConfig = { ...this.currentConfig, ...updates };
      const validation = this.validateConfig(newConfig);

      if (!validation.isValid) {
        return validation;
      }

      this.currentConfig = newConfig;
      await this.saveToStorage();
      this.notifyListeners();

      return { isValid: true, errors: [], warnings: [] };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Failed to update configuration: ${(error as Error).message}`],
        warnings: []
      };
    }
  }

  /**
   * Reset to environment defaults
   */
  async resetToDefaults(): Promise<void> {
    this.currentConfig = this.createDefaultConfig();
    await this.saveToStorage();
    this.notifyListeners();
  }

  /**
   * Switch environment
   */
  async switchEnvironment(environment: 'development' | 'production' | 'test'): Promise<void> {
    this.environmentConfig = this.getEnvironmentByName(environment);
    this.currentConfig = this.createDefaultConfig();
    await this.saveToStorage();
    this.notifyListeners();
  }

  /**
   * Add configuration change listener
   */
  addListener(listener: (config: ExtensionConfig) => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove configuration change listener
   */
  removeListener(listener: (config: ExtensionConfig) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Get configuration for specific feature
   */
  getFeatureConfig(feature: ExtensionFeature): {
    enabled: boolean;
    settings: Record<string, unknown>;
  } {
    return {
      enabled: this.currentConfig.enabledFeatures.includes(feature),
      settings: this.getFeatureSettings(feature)
    };
  }

  /**
   * Toggle feature on/off
   */
  async toggleFeature(feature: ExtensionFeature, enabled?: boolean): Promise<boolean> {
    const isCurrentlyEnabled = this.currentConfig.enabledFeatures.includes(feature);
    const shouldEnable = enabled !== undefined ? enabled : !isCurrentlyEnabled;

    let newFeatures: ExtensionFeature[];

    if (shouldEnable && !isCurrentlyEnabled) {
      newFeatures = [...this.currentConfig.enabledFeatures, feature];
    } else if (!shouldEnable && isCurrentlyEnabled) {
      newFeatures = this.currentConfig.enabledFeatures.filter(f => f !== feature);
    } else {
      return isCurrentlyEnabled;
    }

    const result = await this.updateConfig({ enabledFeatures: newFeatures });
    return result.isValid ? shouldEnable : isCurrentlyEnabled;
  }

  /**
   * Add domain to allowed list
   */
  async addDomain(domain: string): Promise<boolean> {
    const validation = validators.domain.validate(domain);
    if (!validation.isValid || !validation.data) {
      return false;
    }

    const normalizedDomain = validation.data;
    if (this.currentConfig.allowedDomains.includes(normalizedDomain)) {
      return true;
    }

    const newDomains = [...this.currentConfig.allowedDomains, normalizedDomain];
    const result = await this.updateConfig({ allowedDomains: newDomains });
    return result.isValid;
  }

  /**
   * Remove domain from allowed list
   */
  async removeDomain(domain: string): Promise<boolean> {
    const essentialDomains = ['localhost', '127.0.0.1', 'file'];
    if (essentialDomains.includes(domain)) {
      return false; // Cannot remove essential domains
    }

    const newDomains = this.currentConfig.allowedDomains.filter(d => d !== domain);
    const result = await this.updateConfig({ allowedDomains: newDomains });
    return result.isValid;
  }

  /**
   * Check if domain is allowed
   */
  isDomainAllowed(domain: string): boolean {
    if (!domain) return false;

    const validation = validators.domain.validate(domain);
    if (!validation.isValid || !validation.data) return false;

    const normalizedDomain = validation.data;

    // Direct match
    if (this.currentConfig.allowedDomains.includes(normalizedDomain)) {
      return true;
    }

    // Subdomain check
    return this.currentConfig.allowedDomains.some(allowed =>
      normalizedDomain.endsWith('.' + allowed)
    );
  }

  /**
   * Export configuration
   */
  exportConfig(): string {
    return JSON.stringify({
      config: this.currentConfig,
      environment: this.environmentConfig.name,
      exportedAt: new Date().toISOString(),
      version: '2.0.0'
    }, null, 2);
  }

  /**
   * Import configuration
   */
  async importConfig(configJson: string): Promise<ValidationResult> {
    try {
      const imported = JSON.parse(configJson);
      
      if (!imported.config) {
        return {
          isValid: false,
          errors: ['Invalid configuration format'],
          warnings: []
        };
      }

      const validation = this.validateConfig(imported.config);
      if (!validation.isValid) {
        return validation;
      }

      await this.updateConfig(imported.config);
      return { isValid: true, errors: [], warnings: [] };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Import failed: ${(error as Error).message}`],
        warnings: []
      };
    }
  }

  // ============================================================================
  // Private Methods
  // ============================================================================

  private detectEnvironment(): EnvironmentConfig {
    // Try to detect environment from various sources
    const manifestVersion = chrome.runtime.getManifest().version;
    const isDev = manifestVersion.includes('dev') || manifestVersion.includes('beta');
    
    if (isDev) {
      return DEVELOPMENT_CONFIG;
    }

    // Default to production
    return PRODUCTION_CONFIG;
  }

  private getEnvironmentByName(name: string): EnvironmentConfig {
    switch (name) {
      case 'development':
        return DEVELOPMENT_CONFIG;
      case 'production':
        return PRODUCTION_CONFIG;
      case 'test':
        return TEST_CONFIG;
      default:
        return PRODUCTION_CONFIG;
    }
  }

  private createDefaultConfig(): ExtensionConfig {
    return {
      mcpServerUrl: this.environmentConfig.mcpServerUrl,
      allowedDomains: [...this.environmentConfig.allowedDomains],
      enabledFeatures: [...this.environmentConfig.enabledFeatures],
      extensionEnabled: true,
      autoSyncDomains: true,
      debugMode: this.environmentConfig.debugMode,
      apiVersion: '2.1.0'
    };
  }

  private validateConfig(config: Partial<ExtensionConfig>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate mcpServerUrl
    if (config.mcpServerUrl) {
      const urlValidation = validators.url.validate(config.mcpServerUrl);
      if (!urlValidation.isValid) {
        errors.push(`Invalid MCP server URL: ${urlValidation.errors.join(', ')}`);
      }
    }

    // Validate allowedDomains
    if (config.allowedDomains) {
      if (!Array.isArray(config.allowedDomains)) {
        errors.push('allowedDomains must be an array');
      } else {
        for (const domain of config.allowedDomains) {
          const domainValidation = validators.domain.validate(domain);
          if (!domainValidation.isValid) {
            errors.push(`Invalid domain '${domain}': ${domainValidation.errors.join(', ')}`);
          }
        }
      }
    }

    // Validate enabledFeatures
    if (config.enabledFeatures) {
      if (!Array.isArray(config.enabledFeatures)) {
        errors.push('enabledFeatures must be an array');
      } else {
        const validFeatures: ExtensionFeature[] = [
          'dom_monitoring', 'smart_click', 'text_extraction',
          'event_analysis', 'form_automation', 'navigation_tracking'
        ];

        for (const feature of config.enabledFeatures) {
          if (!validFeatures.includes(feature as ExtensionFeature)) {
            errors.push(`Invalid feature: ${feature}`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  private mergeConfigs(base: ExtensionConfig, override: Partial<ExtensionConfig>): ExtensionConfig {
    return {
      ...base,
      ...override,
      // Ensure arrays are properly merged
      allowedDomains: override.allowedDomains || base.allowedDomains,
      enabledFeatures: override.enabledFeatures || base.enabledFeatures
    };
  }

  private async loadFromStorage(): Promise<Partial<ExtensionConfig> | null> {
    try {
      const result = await chrome.storage.local.get(this.storageKey);
      return result[this.storageKey] || null;
    } catch (error) {
      console.error('Failed to load configuration from storage:', error);
      return null;
    }
  }

  private async saveToStorage(): Promise<void> {
    try {
      await chrome.storage.local.set({ [this.storageKey]: this.currentConfig });
    } catch (error) {
      console.error('Failed to save configuration to storage:', error);
      throw error;
    }
  }

  private notifyListeners(): void {
    for (const listener of this.listeners) {
      try {
        listener(this.getConfig());
      } catch (error) {
        console.error('Configuration listener error:', error);
      }
    }
  }

  private getFeatureSettings(feature: ExtensionFeature): Record<string, unknown> {
    // Feature-specific settings
    const featureSettings: Record<ExtensionFeature, Record<string, unknown>> = {
      dom_monitoring: {
        throttleMs: 100,
        maxQueueSize: 100,
        observeAttributes: true,
        observeChildList: true,
        observeSubtree: true
      },
      smart_click: {
        defaultConfidence: 0.8,
        highlightDuration: 2000,
        scrollBehavior: 'smooth',
        timeout: 5000
      },
      text_extraction: {
        maxTextLength: 1000,
        includeHidden: false,
        preserveFormatting: false
      },
      event_analysis: {
        enableAnalytics: this.environmentConfig.debugMode,
        trackClicks: true,
        trackForms: true,
        trackNavigation: true
      },
      form_automation: {
        enableAutoFill: false,
        validateInputs: true,
        submitConfirmation: true
      },
      navigation_tracking: {
        trackSPA: true,
        trackHashChanges: true,
        debounceMs: 500
      }
    };

    return featureSettings[feature] || {};
  }
}

// ============================================================================
// Utility Functions
// ============================================================================

export function getConfig(): ExtensionConfig {
  return ConfigurationManager.getInstance().getConfig();
}

export function updateConfig(updates: Partial<ExtensionConfig>): Promise<ValidationResult> {
  return ConfigurationManager.getInstance().updateConfig(updates);
}

export function isDomainAllowed(domain: string): boolean {
  return ConfigurationManager.getInstance().isDomainAllowed(domain);
}

export function isFeatureEnabled(feature: ExtensionFeature): boolean {
  const config = getConfig();
  return config.extensionEnabled && config.enabledFeatures.includes(feature);
}

export function getFeatureConfig(feature: ExtensionFeature) {
  return ConfigurationManager.getInstance().getFeatureConfig(feature);
}

// ============================================================================
// Configuration Presets
// ============================================================================

export const ConfigPresets = {
  minimal: {
    enabledFeatures: ['dom_monitoring', 'smart_click'] as ExtensionFeature[],
    allowedDomains: ['localhost', '127.0.0.1', 'file'],
    debugMode: false
  },
  
  standard: {
    enabledFeatures: [
      'dom_monitoring', 'smart_click', 'text_extraction', 'event_analysis'
    ] as ExtensionFeature[],
    allowedDomains: ['localhost', '127.0.0.1', 'file', 'github.com'],
    debugMode: false
  },
  
  full: {
    enabledFeatures: [
      'dom_monitoring', 'smart_click', 'text_extraction',
      'event_analysis', 'form_automation', 'navigation_tracking'
    ] as ExtensionFeature[],
    allowedDomains: [
      'localhost', '127.0.0.1', 'file', 'github.com',
      'stackoverflow.com', 'developer.mozilla.org'
    ],
    debugMode: true
  }
};

// ============================================================================
// Initialize Configuration
// ============================================================================

// Auto-initialize when module is loaded
ConfigurationManager.getInstance().initialize().catch(error => {
  console.error('Failed to initialize configuration manager:', error);
});

export default ConfigurationManager;