# ScreenMonitorMCP Browser Extension v2.0

Modern, secure, and type-safe browser extension for ScreenMonitorMCP with clean architecture and comprehensive testing.

## 🚀 Features

### Core Capabilities
- **DOM Monitoring**: Real-time monitoring of web page changes
- **Smart Click**: AI-powered element detection and interaction
- **Text Extraction**: Intelligent text extraction from web pages
- **Event Analysis**: Comprehensive web page event tracking
- **Form Automation**: Automated form filling and submission
- **Navigation Tracking**: SPA and traditional navigation monitoring

### Architecture Highlights
- **TypeScript**: Full type safety and modern JavaScript features
- **Dependency Injection**: Clean, testable, and maintainable code
- **Security First**: Input validation, XSS protection, and secure communication
- **Manifest V3**: Latest Chrome extension standards
- **Comprehensive Testing**: Unit tests, integration tests, and mocks

## 📋 Requirements

- Chrome 88+ or Firefox 78+
- Node.js 16+ (for development)
- TypeScript 5.0+

## 🛠️ Installation

### For Users

1. Download the latest release from the releases page
2. Extract the ZIP file
3. Open Chrome and navigate to `chrome://extensions/`
4. Enable "Developer mode"
5. Click "Load unpacked" and select the extracted folder

### For Developers

```bash
# Clone the repository
git clone <repository-url>
cd browser_extension_v2

# Install dependencies
npm install

# Build the extension
npm run build

# For development with hot reload
npm run dev
```

## 🏗️ Architecture

### Project Structure

```
browser_extension_v2/
├── src/
│   ├── types/           # TypeScript type definitions
│   ├── core/            # Dependency injection container
│   ├── services/        # Core business logic services
│   ├── utils/           # Utility functions and helpers
│   ├── config/          # Configuration management
│   ├── background.ts    # Service worker (background script)
│   └── content-script.ts # Content script for DOM interaction
├── tests/               # Test suites
├── manifest.json        # Extension manifest
└── package.json         # Dependencies and scripts
```

### Core Services

#### ConfigService
Manages extension configuration with validation and persistence.

```typescript
import { ConfigService } from './services/ConfigService';

const configService = new ConfigService(logger);
await configService.initialize();

// Update configuration
await configService.updateConfig({
  mcpServerUrl: 'http://localhost:8888',
  enabledFeatures: ['dom_monitoring', 'smart_click']
});

// Check domain permissions
const isAllowed = configService.isDomainAllowed('example.com');
```

#### MessageService
Secure message passing between extension components.

```typescript
import { MessageService } from './services/MessageService';

const messageService = new MessageService(logger);

// Send message to background
const response = await messageService.sendToBackground({
  type: 'DOM_EVENT',
  data: eventData
});

// Register message handler
messageService.addHandler('SMART_CLICK_REQUEST', async (message) => {
  // Handle smart click request
  return { success: true, data: result };
});
```

#### StorageService
Type-safe Chrome storage wrapper with caching.

```typescript
import { StorageService } from './services/StorageService';

const storageService = new StorageService(logger);

// Store data
await storageService.set('user_preferences', preferences);

// Retrieve data
const preferences = await storageService.get('user_preferences', defaultPrefs);

// Listen for changes
storageService.addChangeListener('user_preferences', (newValue) => {
  console.log('Preferences updated:', newValue);
});
```

### Security Features

#### Input Validation
All inputs are validated using comprehensive validators:

```typescript
import { validators } from './utils/Validator';

// Validate URL
const urlResult = validators.url.validate('http://example.com');
if (urlResult.isValid) {
  // Safe to use URL
}

// Validate domain
const domainResult = validators.domain.validate('example.com');

// Validate CSS selector
const selectorResult = validators.cssSelector.validate('#button');
```

#### XSS Protection
Built-in XSS pattern detection and sanitization:

```typescript
import { sanitizeHTML, validateCSPCompliance } from './utils/Validator';

// Sanitize user input
const safeContent = sanitizeHTML(userInput);

// Check CSP compliance
const cspResult = validateCSPCompliance(content);
```

#### Secure Communication
Encrypted message passing with nonce validation:

```typescript
import { SecurityManager } from './utils/Security';

const securityManager = new SecurityManager(logger);

// Create secure message
const secureMessage = await securityManager.createSecureMessage(
  sessionId, 
  data, 
  nonce
);

// Verify secure message
const verifiedData = await securityManager.verifySecureMessage(
  sessionId, 
  secureMessage
);
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- ConfigService.test.ts
```

### Test Structure

```
tests/
├── setup.ts              # Test environment setup
├── services/              # Service tests
│   ├── ConfigService.test.ts
│   ├── MessageService.test.ts
│   └── StorageService.test.ts
├── utils/                 # Utility tests
│   ├── Validator.test.ts
│   └── Security.test.ts
└── integration/           # Integration tests
```

### Writing Tests

```typescript
import { setupTestEnvironment, TestDataFactory } from '../setup';

describe('MyService', () => {
  let cleanup: () => void;

  beforeAll(() => {
    cleanup = setupTestEnvironment();
  });

  afterAll(() => {
    cleanup();
  });

  it('should work correctly', () => {
    const config = TestDataFactory.createExtensionConfig();
    // Test implementation
  });
});
```

## ⚙️ Configuration

### Environment Configuration

The extension supports multiple environments with different configurations:

```typescript
// Development
{
  mcpServerUrl: 'http://localhost:7777',
  debugMode: true,
  enabledFeatures: ['dom_monitoring', 'smart_click', 'text_extraction'],
  allowedDomains: ['localhost', '127.0.0.1', 'github.com']
}

// Production
{
  mcpServerUrl: 'http://localhost:7777',
  debugMode: false,
  enabledFeatures: ['dom_monitoring', 'smart_click'],
  allowedDomains: ['localhost', '127.0.0.1']
}
```

### Runtime Configuration

```typescript
import { getConfig, updateConfig } from './config';

// Get current configuration
const config = getConfig();

// Update configuration
await updateConfig({
  enabledFeatures: ['dom_monitoring', 'smart_click', 'event_analysis']
});

// Check feature status
const isEnabled = isFeatureEnabled('smart_click');
```

### Configuration Presets

```typescript
import { ConfigPresets } from './config';

// Apply minimal preset
await updateConfig(ConfigPresets.minimal);

// Apply full feature preset
await updateConfig(ConfigPresets.full);
```

## 🔧 Development

### Building

```bash
# Development build
npm run build:dev

# Production build
npm run build

# Watch mode for development
npm run watch
```

### Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

### Debugging

1. Enable debug mode in configuration
2. Open Chrome DevTools
3. Check the Console and Network tabs
4. Use the extension's popup for real-time status

## 📡 API Integration

### MCP Server Communication

The extension communicates with the MCP server through RESTful APIs:

```typescript
// Registration
POST /api/extension/register
{
  "domain": "localhost",
  "features": ["dom_monitoring", "smart_click"],
  "session_id": "session_123",
  "user_agent": "Chrome/91.0"
}

// DOM Events
POST /api/extension/dom-event
{
  "session_id": "session_123",
  "event_type": "click",
  "event_data": { ... }
}

// Smart Click
POST /api/extension/smart-click
{
  "session_id": "session_123",
  "element_description": "Click the submit button",
  "confidence_threshold": 0.8
}
```

### Response Format

```typescript
{
  "success": boolean,
  "data": any,
  "error": string | null,
  "timestamp": string,
  "request_id": string
}
```

## 🛡️ Security

### Permissions

The extension requests minimal permissions:

```json
{
  "permissions": [
    "activeTab",
    "storage",
    "scripting",
    "tabs"
  ],
  "host_permissions": [
    "http://localhost:7777/*",
    "https://localhost:7777/*"
  ]
}
```

### Content Security Policy

```
script-src 'self'; object-src 'self'; connect-src 'self' http://localhost:* https://localhost:*
```

### Domain Whitelist

Only allowed domains can be monitored:

- `localhost` (essential)
- `127.0.0.1` (essential)
- `file` (essential)
- User-configured domains

## 🐛 Troubleshooting

### Common Issues

#### Extension Not Loading
1. Check if Manifest V3 is supported
2. Verify all required permissions are granted
3. Check the Chrome extensions page for errors

#### MCP Server Connection Failed
1. Verify MCP server is running on correct port
2. Check firewall settings
3. Ensure domain is in whitelist

#### Smart Click Not Working
1. Verify element is visible and clickable
2. Check confidence threshold settings
3. Try using CSS selector as fallback

### Debug Mode

Enable debug mode for detailed logging:

```typescript
await updateConfig({ debugMode: true });
```

### Logs

Check browser console for detailed logs:
- `[ScreenMonitorMCP-BG]` - Background script logs
- `[ScreenMonitorMCP-CS]` - Content script logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write comprehensive tests
- Document public APIs
- Use semantic commit messages

## 📄 License

MIT License - see LICENSE file for details.

## 🔗 Related Projects

- [ScreenMonitorMCP Server](../README.md) - Main MCP server
- [Web Extension v2](../web_extension_v2/README.md) - Python server component

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Search existing issues
3. Create a new issue with detailed information
4. Include browser version, extension version, and error logs