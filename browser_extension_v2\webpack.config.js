const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    entry: {
      background: './src/background.ts',
      'content-script': './src/content-script.ts',
      popup: './src/popup.js',
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true,
    },
    
    module: {
      rules: [
        {
          test: /\.ts$/,
          use: {
            loader: 'ts-loader',
            options: {
              transpileOnly: true,
              compilerOptions: {
                noEmit: false,
                skipLibCheck: true,
              }
            }
          },
          exclude: /node_modules/,
        },
      ],
    },
    
    resolve: {
      extensions: ['.ts', '.js'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    
    plugins: [
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'manifest.json',
            to: 'manifest.json',
          },
          // Copy HTML files
          {
            from: 'src/*.html',
            to: '[name][ext]',
            noErrorOnMissing: true,
          },
          // Copy icons if they exist
          {
            from: 'icons',
            to: 'icons',
            noErrorOnMissing: true,
          },
        ],
      }),
    ],
    
    devtool: isProduction ? false : 'cheap-module-source-map',
    
    optimization: {
      minimize: isProduction,
    },
    
    // Chrome extension specific settings
    target: 'web',
    
    // Ensure proper module format for Chrome extensions
    experiments: {
      outputModule: false,
    },
  };
};
