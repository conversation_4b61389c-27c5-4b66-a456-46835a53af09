/**
 * ScreenMonitorMCP Browser Extension - Security Utilities
 * 
 * Security utilities for encryption, secure communication,
 * and protection against common web vulnerabilities.
 */

import type { Logger } from '../types';

// ============================================================================
// Security Configuration
// ============================================================================

interface SecurityConfig {
  readonly enableEncryption: boolean;
  readonly enableCSP: boolean;
  readonly enableRateLimit: boolean;
  readonly maxMessageSize: number;
  readonly allowedOrigins: string[];
  readonly trustedDomains: string[];
}

const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  enableEncryption: true,
  enableCSP: true,
  enableRateLimit: true,
  maxMessageSize: 1024 * 1024, // 1MB
  allowedOrigins: ['chrome-extension://', 'moz-extension://'],
  trustedDomains: ['localhost', '127.0.0.1'],
};

// ============================================================================
// Security Manager
// ============================================================================

export class SecurityManager {
  private readonly config: SecurityConfig;
  private readonly logger: Logger;
  private readonly nonces = new Set<string>();
  private readonly sessionKeys = new Map<string, CryptoKey>();

  constructor(logger: Logger, config: Partial<SecurityConfig> = {}) {
    this.config = { ...DEFAULT_SECURITY_CONFIG, ...config };
    this.logger = logger.createChild({ service: 'SecurityManager' });
  }

  /**
   * Generate a cryptographically secure nonce
   */
  generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    const nonce = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    
    this.nonces.add(nonce);
    
    // Clean up old nonces (keep only last 1000)
    if (this.nonces.size > 1000) {
      const noncesArray = Array.from(this.nonces);
      this.nonces.clear();
      noncesArray.slice(-1000).forEach(n => this.nonces.add(n));
    }
    
    return nonce;
  }

  /**
   * Verify nonce validity
   */
  verifyNonce(nonce: string): boolean {
    return this.nonces.has(nonce);
  }

  /**
   * Consume nonce (one-time use)
   */
  consumeNonce(nonce: string): boolean {
    if (this.nonces.has(nonce)) {
      this.nonces.delete(nonce);
      return true;
    }
    return false;
  }

  /**
   * Generate session key for encryption
   */
  async generateSessionKey(sessionId: string): Promise<CryptoKey> {
    try {
      const key = await crypto.subtle.generateKey(
        {
          name: 'AES-GCM',
          length: 256,
        },
        true,
        ['encrypt', 'decrypt']
      );

      this.sessionKeys.set(sessionId, key);
      this.logger.debug('Session key generated', { sessionId });
      
      return key;
    } catch (error) {
      this.logger.error('Failed to generate session key', error as Error);
      throw new Error('Key generation failed');
    }
  }

  /**
   * Encrypt data using session key
   */
  async encryptData(sessionId: string, data: string): Promise<string> {
    if (!this.config.enableEncryption) {
      return data;
    }

    try {
      const key = this.sessionKeys.get(sessionId);
      if (!key) {
        throw new Error('Session key not found');
      }

      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        key,
        dataBuffer
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encryptedBuffer.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encryptedBuffer), iv.length);

      // Convert to base64
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      this.logger.error('Encryption failed', error as Error);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt data using session key
   */
  async decryptData(sessionId: string, encryptedData: string): Promise<string> {
    if (!this.config.enableEncryption) {
      return encryptedData;
    }

    try {
      const key = this.sessionKeys.get(sessionId);
      if (!key) {
        throw new Error('Session key not found');
      }

      // Convert from base64
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      // Extract IV and encrypted data
      const iv = combined.slice(0, 12);
      const encryptedBuffer = combined.slice(12);

      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        key,
        encryptedBuffer
      );

      const decoder = new TextDecoder();
      return decoder.decode(decryptedBuffer);
    } catch (error) {
      this.logger.error('Decryption failed', error as Error);
      throw new Error('Decryption failed');
    }
  }

  /**
   * Validate message origin
   */
  validateOrigin(origin: string): boolean {
    if (!origin) {
      return false;
    }

    // Check allowed origins
    const isAllowedOrigin = this.config.allowedOrigins.some(allowed => 
      origin.startsWith(allowed)
    );

    if (isAllowedOrigin) {
      return true;
    }

    // Check trusted domains for web origins
    try {
      const url = new URL(origin);
      return this.config.trustedDomains.includes(url.hostname);
    } catch {
      return false;
    }
  }

  /**
   * Validate message size
   */
  validateMessageSize(message: string): boolean {
    return message.length <= this.config.maxMessageSize;
  }

  /**
   * Create secure message wrapper
   */
  async createSecureMessage(
    sessionId: string,
    data: any,
    nonce?: string
  ): Promise<{
    data: string;
    nonce: string;
    timestamp: string;
    signature?: string;
  }> {
    const messageNonce = nonce || this.generateNonce();
    const timestamp = new Date().toISOString();
    
    const payload = JSON.stringify({
      data,
      timestamp,
      nonce: messageNonce,
    });

    const encryptedData = await this.encryptData(sessionId, payload);

    return {
      data: encryptedData,
      nonce: messageNonce,
      timestamp,
    };
  }

  /**
   * Verify and unwrap secure message
   */
  async verifySecureMessage(
    sessionId: string,
    secureMessage: {
      data: string;
      nonce: string;
      timestamp: string;
    }
  ): Promise<any> {
    // Verify nonce
    if (!this.consumeNonce(secureMessage.nonce)) {
      throw new Error('Invalid or expired nonce');
    }

    // Verify timestamp (within 5 minutes)
    const messageTime = new Date(secureMessage.timestamp).getTime();
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    if (now - messageTime > maxAge) {
      throw new Error('Message expired');
    }

    // Decrypt data
    const decryptedPayload = await this.decryptData(sessionId, secureMessage.data);
    const payload = JSON.parse(decryptedPayload);

    // Verify payload integrity
    if (payload.nonce !== secureMessage.nonce || 
        payload.timestamp !== secureMessage.timestamp) {
      throw new Error('Message integrity check failed');
    }

    return payload.data;
  }

  /**
   * Clean up session resources
   */
  cleanupSession(sessionId: string): void {
    this.sessionKeys.delete(sessionId);
    this.logger.debug('Session cleaned up', { sessionId });
  }

  /**
   * Get security statistics
   */
  getSecurityStats(): {
    activeNonces: number;
    activeSessions: number;
    encryptionEnabled: boolean;
    cspEnabled: boolean;
  } {
    return {
      activeNonces: this.nonces.size,
      activeSessions: this.sessionKeys.size,
      encryptionEnabled: this.config.enableEncryption,
      cspEnabled: this.config.enableCSP,
    };
  }
}

// ============================================================================
// Content Security Policy Utilities
// ============================================================================

export class CSPManager {
  private readonly logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger.createChild({ service: 'CSPManager' });
  }

  /**
   * Generate CSP header for extension pages
   */
  generateCSPHeader(): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "connect-src 'self' http://localhost:* https://localhost:*",
      "font-src 'self'",
      "object-src 'none'",
      "media-src 'self'",
      "frame-src 'none'",
      "worker-src 'self'",
      "child-src 'self'",
      "form-action 'self'",
      "base-uri 'self'",
      "manifest-src 'self'"
    ];

    return directives.join('; ');
  }

  /**
   * Validate content against CSP
   */
  validateContent(content: string): {
    isValid: boolean;
    violations: string[];
  } {
    const violations: string[] = [];

    // Check for inline scripts
    if (/<script\b[^>]*>[\s\S]*?<\/script>/gi.test(content)) {
      violations.push('Inline scripts detected');
    }

    // Check for inline event handlers
    if (/\son\w+\s*=/gi.test(content)) {
      violations.push('Inline event handlers detected');
    }

    // Check for javascript: URLs
    if (/javascript:/gi.test(content)) {
      violations.push('JavaScript URLs detected');
    }

    // Check for eval usage
    if (/\beval\s*\(/gi.test(content)) {
      violations.push('eval() usage detected');
    }

    return {
      isValid: violations.length === 0,
      violations,
    };
  }
}

// ============================================================================
// Permission Manager
// ============================================================================

export class PermissionManager {
  private readonly logger: Logger;
  private readonly permissions = new Map<string, Set<string>>();

  constructor(logger: Logger) {
    this.logger = logger.createChild({ service: 'PermissionManager' });
  }

  /**
   * Grant permission to session
   */
  grantPermission(sessionId: string, permission: string): void {
    if (!this.permissions.has(sessionId)) {
      this.permissions.set(sessionId, new Set());
    }
    
    this.permissions.get(sessionId)!.add(permission);
    this.logger.debug('Permission granted', { sessionId, permission });
  }

  /**
   * Revoke permission from session
   */
  revokePermission(sessionId: string, permission: string): void {
    const sessionPermissions = this.permissions.get(sessionId);
    if (sessionPermissions) {
      sessionPermissions.delete(permission);
      this.logger.debug('Permission revoked', { sessionId, permission });
    }
  }

  /**
   * Check if session has permission
   */
  hasPermission(sessionId: string, permission: string): boolean {
    const sessionPermissions = this.permissions.get(sessionId);
    return sessionPermissions ? sessionPermissions.has(permission) : false;
  }

  /**
   * Get all permissions for session
   */
  getPermissions(sessionId: string): string[] {
    const sessionPermissions = this.permissions.get(sessionId);
    return sessionPermissions ? Array.from(sessionPermissions) : [];
  }

  /**
   * Clear all permissions for session
   */
  clearPermissions(sessionId: string): void {
    this.permissions.delete(sessionId);
    this.logger.debug('Permissions cleared', { sessionId });
  }

  /**
   * Validate required permissions
   */
  validatePermissions(sessionId: string, requiredPermissions: string[]): boolean {
    return requiredPermissions.every(permission => 
      this.hasPermission(sessionId, permission)
    );
  }
}

// ============================================================================
// Security Audit Logger
// ============================================================================

export class SecurityAuditLogger {
  private readonly logger: Logger;
  private readonly auditLog: Array<{
    timestamp: string;
    event: string;
    sessionId?: string;
    details: Record<string, unknown>;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }> = [];

  constructor(logger: Logger) {
    this.logger = logger.createChild({ service: 'SecurityAudit' });
  }

  /**
   * Log security event
   */
  logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details: Record<string, unknown> = {},
    sessionId?: string
  ): void {
    const auditEntry = {
      timestamp: new Date().toISOString(),
      event,
      sessionId,
      details,
      severity,
    };

    this.auditLog.push(auditEntry);

    // Keep only last 1000 entries
    if (this.auditLog.length > 1000) {
      this.auditLog.shift();
    }

    // Log based on severity
    switch (severity) {
      case 'critical':
        this.logger.error(`SECURITY CRITICAL: ${event}`, undefined, details);
        break;
      case 'high':
        this.logger.error(`SECURITY HIGH: ${event}`, undefined, details);
        break;
      case 'medium':
        this.logger.warn(`SECURITY MEDIUM: ${event}`, details);
        break;
      case 'low':
        this.logger.info(`SECURITY LOW: ${event}`, details);
        break;
    }
  }

  /**
   * Get audit log entries
   */
  getAuditLog(severity?: 'low' | 'medium' | 'high' | 'critical'): typeof this.auditLog {
    if (severity) {
      return this.auditLog.filter(entry => entry.severity === severity);
    }
    return [...this.auditLog];
  }

  /**
   * Clear audit log
   */
  clearAuditLog(): void {
    this.auditLog.length = 0;
    this.logger.info('Security audit log cleared');
  }
}

// ============================================================================
// Global Security Instance
// ============================================================================

let globalSecurityManager: SecurityManager | null = null;
let globalCSPManager: CSPManager | null = null;
let globalPermissionManager: PermissionManager | null = null;
let globalAuditLogger: SecurityAuditLogger | null = null;

export function initializeSecurity(logger: Logger, config?: Partial<SecurityConfig>): {
  securityManager: SecurityManager;
  cspManager: CSPManager;
  permissionManager: PermissionManager;
  auditLogger: SecurityAuditLogger;
} {
  globalSecurityManager = new SecurityManager(logger, config);
  globalCSPManager = new CSPManager(logger);
  globalPermissionManager = new PermissionManager(logger);
  globalAuditLogger = new SecurityAuditLogger(logger);

  logger.info('Security system initialized');

  return {
    securityManager: globalSecurityManager,
    cspManager: globalCSPManager,
    permissionManager: globalPermissionManager,
    auditLogger: globalAuditLogger,
  };
}

export function getSecurityManager(): SecurityManager {
  if (!globalSecurityManager) {
    throw new Error('Security manager not initialized');
  }
  return globalSecurityManager;
}

export function getCSPManager(): CSPManager {
  if (!globalCSPManager) {
    throw new Error('CSP manager not initialized');
  }
  return globalCSPManager;
}

export function getPermissionManager(): PermissionManager {
  if (!globalPermissionManager) {
    throw new Error('Permission manager not initialized');
  }
  return globalPermissionManager;
}

export function getAuditLogger(): SecurityAuditLogger {
  if (!globalAuditLogger) {
    throw new Error('Audit logger not initialized');
  }
  return globalAuditLogger;
}