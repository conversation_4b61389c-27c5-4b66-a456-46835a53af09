{"name": "jest-environment-jsdom", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-environment-jsdom"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/jsdom": "^20.0.0", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0", "jsdom": "^20.0.0"}, "devDependencies": {"@jest/test-utils": "^29.7.0"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}