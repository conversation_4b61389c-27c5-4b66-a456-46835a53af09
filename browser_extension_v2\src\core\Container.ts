/**
 * ScreenMonitorMCP Browser Extension - Dependency Injection Container
 * 
 * Modern dependency injection container for managing extension services
 * and providing clean separation of concerns.
 */

import type { Logger } from '../types';

// ============================================================================
// Container Types
// ============================================================================

type Constructor<T = {}> = new (...args: any[]) => T;
type Factory<T> = () => T | Promise<T>;
type ServiceKey = string | symbol;

interface ServiceDefinition<T = unknown> {
  factory: Factory<T>;
  singleton: boolean;
  dependencies: ServiceKey[];
}

interface ServiceInstance<T = unknown> {
  instance: T;
  created: Date;
}

// ============================================================================
// Dependency Injection Container
// ============================================================================

export class Container {
  private readonly services = new Map<ServiceKey, ServiceDefinition>();
  private readonly instances = new Map<ServiceKey, ServiceInstance>();
  private readonly logger?: Logger;

  constructor(logger?: Logger) {
    this.logger = logger;
  }

  /**
   * Register a service with the container
   */
  register<T>(
    key: ServiceKey,
    factory: Factory<T>,
    options: { singleton?: boolean; dependencies?: ServiceKey[] } = {}
  ): this {
    const { singleton = true, dependencies = [] } = options;

    this.services.set(key, {
      factory,
      singleton,
      dependencies,
    });

    this.logger?.debug('Service registered', { key: key.toString(), singleton });
    return this;
  }

  /**
   * Register a class as a service
   */
  registerClass<T>(
    key: ServiceKey,
    constructor: Constructor<T>,
    options: { singleton?: boolean; dependencies?: ServiceKey[] } = {}
  ): this {
    return this.register(
      key,
      () => new constructor(...options.dependencies?.map(dep => this.resolve(dep)) || []),
      options
    );
  }

  /**
   * Register a singleton instance
   */
  registerInstance<T>(key: ServiceKey, instance: T): this {
    this.instances.set(key, {
      instance,
      created: new Date(),
    });

    this.logger?.debug('Instance registered', { key: key.toString() });
    return this;
  }

  /**
   * Resolve a service from the container
   */
  resolve<T>(key: ServiceKey): T {
    // Check if instance already exists
    const existingInstance = this.instances.get(key);
    if (existingInstance) {
      return existingInstance.instance as T;
    }

    // Get service definition
    const service = this.services.get(key);
    if (!service) {
      throw new Error(`Service not found: ${key.toString()}`);
    }

    try {
      // Resolve dependencies first
      const dependencies = service.dependencies.map(dep => this.resolve(dep));
      
      // Create instance
      const instance = service.factory();
      
      // Store if singleton
      if (service.singleton) {
        this.instances.set(key, {
          instance,
          created: new Date(),
        });
      }

      this.logger?.debug('Service resolved', { 
        key: key.toString(), 
        singleton: service.singleton 
      });

      return instance as T;
    } catch (error) {
      this.logger?.error('Failed to resolve service', error as Error, { 
        key: key.toString() 
      });
      throw new Error(`Failed to resolve service: ${key.toString()}`);
    }
  }

  /**
   * Check if a service is registered
   */
  has(key: ServiceKey): boolean {
    return this.services.has(key) || this.instances.has(key);
  }

  /**
   * Remove a service from the container
   */
  remove(key: ServiceKey): boolean {
    const serviceRemoved = this.services.delete(key);
    const instanceRemoved = this.instances.delete(key);
    
    if (serviceRemoved || instanceRemoved) {
      this.logger?.debug('Service removed', { key: key.toString() });
    }
    
    return serviceRemoved || instanceRemoved;
  }

  /**
   * Clear all services and instances
   */
  clear(): void {
    this.services.clear();
    this.instances.clear();
    this.logger?.debug('Container cleared');
  }

  /**
   * Get container statistics
   */
  getStats(): {
    registeredServices: number;
    activeInstances: number;
    services: string[];
    instances: string[];
  } {
    return {
      registeredServices: this.services.size,
      activeInstances: this.instances.size,
      services: Array.from(this.services.keys()).map(k => k.toString()),
      instances: Array.from(this.instances.keys()).map(k => k.toString()),
    };
  }
}

// ============================================================================
// Service Keys (Symbols for type safety)
// ============================================================================

export const ServiceKeys = {
  // Core services
  LOGGER: Symbol('Logger'),
  CONFIG_SERVICE: Symbol('ConfigService'),
  STORAGE_SERVICE: Symbol('StorageService'),
  
  // Communication services
  MESSAGE_SERVICE: Symbol('MessageService'),
  MCP_CLIENT: Symbol('MCPClient'),
  
  // Feature services
  DOM_MONITOR: Symbol('DOMMonitor'),
  SMART_CLICKER: Symbol('SmartClicker'),
  EVENT_ANALYZER: Symbol('EventAnalyzer'),
  
  // Validation services
  CONFIG_VALIDATOR: Symbol('ConfigValidator'),
  MESSAGE_VALIDATOR: Symbol('MessageValidator'),
  
  // Background services
  SESSION_MANAGER: Symbol('SessionManager'),
  DOMAIN_MANAGER: Symbol('DomainManager'),
} as const;

// ============================================================================
// Global Container Instance
// ============================================================================

let globalContainer: Container | null = null;

export function getContainer(): Container {
  if (!globalContainer) {
    throw new Error('Container not initialized. Call initializeContainer() first.');
  }
  return globalContainer;
}

export function initializeContainer(logger?: Logger): Container {
  if (globalContainer) {
    logger?.warn('Container already initialized');
    return globalContainer;
  }
  
  globalContainer = new Container(logger);
  logger?.info('Container initialized');
  return globalContainer;
}

export function resetContainer(): void {
  if (globalContainer) {
    globalContainer.clear();
    globalContainer = null;
  }
}

// ============================================================================
// Decorator for Dependency Injection (Future Enhancement)
// ============================================================================

export function Injectable(key?: ServiceKey) {
  return function <T extends Constructor>(constructor: T) {
    const serviceKey = key || constructor.name;
    
    // This would be used with a build-time transformer
    // For now, services need to be registered manually
    return constructor;
  };
}

export function Inject(key: ServiceKey) {
  return function (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
    // This would be used with a build-time transformer
    // For now, dependencies need to be resolved manually
  };
}