/**
 * ScreenMonitorMCP Browser Extension - Configuration Service
 * 
 * Centralized configuration management with validation, defaults,
 * and runtime updates. Provides type-safe configuration access.
 */

import type { ExtensionConfig, ExtensionFeature, Logger, ValidationResult } from '../types';

// ============================================================================
// Configuration Defaults
// ============================================================================

const DEFAULT_CONFIG: ExtensionConfig = {
  mcpServerUrl: 'http://localhost:7777',
  allowedDomains: ['localhost', '127.0.0.1', 'github.com'],
  enabledFeatures: ['dom_monitoring', 'smart_click', 'text_extraction', 'event_analysis'],
  extensionEnabled: true,
  autoSyncDomains: true,
  debugMode: false,
  apiVersion: '2.1.0',
};

const ESSENTIAL_DOMAINS = ['localhost', '127.0.0.1', 'file'];

// ============================================================================
// Configuration Service
// ============================================================================

export class ConfigService {
  private config: ExtensionConfig = { ...DEFAULT_CONFIG };
  private readonly logger: Logger;
  private readonly storageKey = 'screenmonitor_config';

  constructor(logger: Logger) {
    this.logger = logger.createChild({ service: 'ConfigService' });
  }

  /**
   * Initialize configuration from storage
   */
  async initialize(): Promise<void> {
    try {
      this.logger.debug('Initializing configuration');
      
      const stored = await this.loadFromStorage();
      if (stored) {
        const validation = this.validateConfig(stored);
        if (validation.isValid) {
          this.config = this.mergeWithDefaults(stored);
          this.logger.info('Configuration loaded from storage');
        } else {
          this.logger.warn('Invalid stored configuration, using defaults', {
            errors: validation.errors,
          });
          await this.saveToStorage();
        }
      } else {
        this.logger.info('No stored configuration found, using defaults');
        await this.saveToStorage();
      }

      this.ensureEssentialDomains();
      this.logger.debug('Configuration initialized', { config: this.config });
    } catch (error) {
      this.logger.error('Failed to initialize configuration', error as Error);
      throw new Error('Configuration initialization failed');
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): ExtensionConfig {
    return { ...this.config };
  }

  /**
   * Get specific configuration value
   */
  get<K extends keyof ExtensionConfig>(key: K): ExtensionConfig[K] {
    return this.config[key];
  }

  /**
   * Update configuration
   */
  async updateConfig(updates: Partial<ExtensionConfig>): Promise<ValidationResult> {
    try {
      this.logger.debug('Updating configuration', { updates });

      const newConfig = { ...this.config, ...updates };
      const validation = this.validateConfig(newConfig);

      if (!validation.isValid) {
        this.logger.warn('Configuration update validation failed', {
          errors: validation.errors,
        });
        return validation;
      }

      const oldConfig = { ...this.config };
      this.config = newConfig;
      this.ensureEssentialDomains();

      await this.saveToStorage();
      
      this.logger.info('Configuration updated successfully', {
        changes: this.getConfigChanges(oldConfig, this.config),
      });

      return { isValid: true, errors: [], warnings: [] };
    } catch (error) {
      this.logger.error('Failed to update configuration', error as Error);
      return {
        isValid: false,
        errors: ['Failed to update configuration'],
        warnings: [],
      };
    }
  }

  /**
   * Reset configuration to defaults
   */
  async resetToDefaults(): Promise<void> {
    try {
      this.logger.info('Resetting configuration to defaults');
      this.config = { ...DEFAULT_CONFIG };
      await this.saveToStorage();
    } catch (error) {
      this.logger.error('Failed to reset configuration', error as Error);
      throw new Error('Configuration reset failed');
    }
  }

  /**
   * Add domain to allowed list
   */
  async addDomain(domain: string): Promise<boolean> {
    try {
      const normalizedDomain = this.normalizeDomain(domain);
      if (!normalizedDomain) {
        this.logger.warn('Invalid domain format', { domain });
        return false;
      }

      if (this.config.allowedDomains.includes(normalizedDomain)) {
        this.logger.debug('Domain already in allowed list', { domain: normalizedDomain });
        return true;
      }

      const newDomains = [...this.config.allowedDomains, normalizedDomain];
      await this.updateConfig({ allowedDomains: newDomains });
      
      this.logger.info('Domain added to allowed list', { domain: normalizedDomain });
      return true;
    } catch (error) {
      this.logger.error('Failed to add domain', error as Error, { domain });
      return false;
    }
  }

  /**
   * Remove domain from allowed list
   */
  async removeDomain(domain: string): Promise<boolean> {
    try {
      const normalizedDomain = this.normalizeDomain(domain);
      if (!normalizedDomain) {
        return false;
      }

      // Don't allow removal of essential domains
      if (ESSENTIAL_DOMAINS.includes(normalizedDomain)) {
        this.logger.warn('Cannot remove essential domain', { domain: normalizedDomain });
        return false;
      }

      const newDomains = this.config.allowedDomains.filter(d => d !== normalizedDomain);
      await this.updateConfig({ allowedDomains: newDomains });
      
      this.logger.info('Domain removed from allowed list', { domain: normalizedDomain });
      return true;
    } catch (error) {
      this.logger.error('Failed to remove domain', error as Error, { domain });
      return false;
    }
  }

  /**
   * Check if domain is allowed
   */
  isDomainAllowed(domain: string): boolean {
    if (!domain) return false;

    const normalizedDomain = this.normalizeDomain(domain);
    if (!normalizedDomain) return false;

    // Direct match
    if (this.config.allowedDomains.includes(normalizedDomain)) {
      return true;
    }

    // Subdomain check
    return this.config.allowedDomains.some(allowed => 
      normalizedDomain.endsWith('.' + allowed)
    );
  }

  /**
   * Toggle feature on/off
   */
  async toggleFeature(feature: ExtensionFeature, enabled?: boolean): Promise<boolean> {
    try {
      const isCurrentlyEnabled = this.config.enabledFeatures.includes(feature);
      const shouldEnable = enabled !== undefined ? enabled : !isCurrentlyEnabled;

      let newFeatures: ExtensionFeature[];
      
      if (shouldEnable && !isCurrentlyEnabled) {
        newFeatures = [...this.config.enabledFeatures, feature];
      } else if (!shouldEnable && isCurrentlyEnabled) {
        newFeatures = this.config.enabledFeatures.filter(f => f !== feature);
      } else {
        return isCurrentlyEnabled; // No change needed
      }

      await this.updateConfig({ enabledFeatures: newFeatures });
      
      this.logger.info('Feature toggled', { feature, enabled: shouldEnable });
      return shouldEnable;
    } catch (error) {
      this.logger.error('Failed to toggle feature', error as Error, { feature });
      return false;
    }
  }

  /**
   * Check if feature is enabled
   */
  isFeatureEnabled(feature: ExtensionFeature): boolean {
    return this.config.extensionEnabled && this.config.enabledFeatures.includes(feature);
  }

  // ============================================================================
  // Private Methods
  // ============================================================================

  private async loadFromStorage(): Promise<Partial<ExtensionConfig> | null> {
    try {
      const result = await chrome.storage.local.get(this.storageKey);
      return result[this.storageKey] || null;
    } catch (error) {
      this.logger.error('Failed to load configuration from storage', error as Error);
      return null;
    }
  }

  private async saveToStorage(): Promise<void> {
    try {
      await chrome.storage.local.set({ [this.storageKey]: this.config });
    } catch (error) {
      this.logger.error('Failed to save configuration to storage', error as Error);
      throw error;
    }
  }

  private validateConfig(config: Partial<ExtensionConfig>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate mcpServerUrl
    if (config.mcpServerUrl) {
      try {
        new URL(config.mcpServerUrl);
      } catch {
        errors.push('Invalid MCP server URL format');
      }
    }

    // Validate allowedDomains
    if (config.allowedDomains) {
      if (!Array.isArray(config.allowedDomains)) {
        errors.push('allowedDomains must be an array');
      } else if (config.allowedDomains.length === 0) {
        warnings.push('No allowed domains specified');
      }
    }

    // Validate enabledFeatures
    if (config.enabledFeatures) {
      if (!Array.isArray(config.enabledFeatures)) {
        errors.push('enabledFeatures must be an array');
      } else {
        const validFeatures: ExtensionFeature[] = [
          'dom_monitoring', 'smart_click', 'text_extraction', 
          'event_analysis', 'form_automation', 'navigation_tracking'
        ];
        
        const invalidFeatures = config.enabledFeatures.filter(
          feature => !validFeatures.includes(feature as ExtensionFeature)
        );
        
        if (invalidFeatures.length > 0) {
          errors.push(`Invalid features: ${invalidFeatures.join(', ')}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private mergeWithDefaults(config: Partial<ExtensionConfig>): ExtensionConfig {
    return {
      ...DEFAULT_CONFIG,
      ...config,
    };
  }

  private ensureEssentialDomains(): void {
    const missing = ESSENTIAL_DOMAINS.filter(
      domain => !this.config.allowedDomains.includes(domain)
    );

    if (missing.length > 0) {
      this.config.allowedDomains.push(...missing);
      this.logger.debug('Added essential domains', { domains: missing });
    }
  }

  private normalizeDomain(domain: string): string | null {
    if (!domain || typeof domain !== 'string') {
      return null;
    }

    // Remove protocol and path
    let normalized = domain.toLowerCase().trim();
    normalized = normalized.replace(/^https?:\/\//, '');
    normalized = normalized.split('/')[0];
    normalized = normalized.split('?')[0];
    normalized = normalized.split('#')[0];

    // Basic validation
    if (!normalized || normalized.includes(' ')) {
      return null;
    }

    return normalized;
  }

  private getConfigChanges(
    oldConfig: ExtensionConfig, 
    newConfig: ExtensionConfig
  ): Record<string, { old: unknown; new: unknown }> {
    const changes: Record<string, { old: unknown; new: unknown }> = {};

    for (const key in newConfig) {
      const typedKey = key as keyof ExtensionConfig;
      if (JSON.stringify(oldConfig[typedKey]) !== JSON.stringify(newConfig[typedKey])) {
        changes[key] = {
          old: oldConfig[typedKey],
          new: newConfig[typedKey],
        };
      }
    }

    return changes;
  }
}