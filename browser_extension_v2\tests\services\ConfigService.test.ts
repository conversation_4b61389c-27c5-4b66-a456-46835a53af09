/**
 * ConfigService Tests
 */

import { ConfigService } from '../../src/services/ConfigService';
import { createLogger } from '../../src/utils/Logger';
import { setupTestEnvironment, TestDataFactory, TestUtils } from '../setup';
import type { ExtensionConfig } from '../../src/types';

describe('ConfigService', () => {
  let configService: ConfigService;
  let mockLogger: any;
  let cleanup: () => void;

  beforeAll(() => {
    cleanup = setupTestEnvironment();
  });

  afterAll(() => {
    cleanup();
  });

  beforeEach(() => {
    mockLogger = {
      createChild: jest.fn().mockReturnThis(),
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    configService = new ConfigService(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default configuration', async () => {
      await configService.initialize();
      
      const config = configService.getConfig();
      expect(config).toBeDefined();
      expect(config.mcpServerUrl).toBe('http://localhost:7777');
      expect(config.extensionEnabled).toBe(true);
      expect(config.allowedDomains).toContain('localhost');
    });

    it('should load configuration from storage if available', async () => {
      const storedConfig = TestDataFactory.createExtensionConfig();
      storedConfig.mcpServerUrl = 'http://custom:8888';
      
      (chrome.storage.local.get as jest.Mock).mockResolvedValue({
        screenmonitor_config: storedConfig
      });

      await configService.initialize();
      
      const config = configService.getConfig();
      expect(config.mcpServerUrl).toBe('http://custom:8888');
    });

    it('should use defaults if stored configuration is invalid', async () => {
      const invalidConfig = { mcpServerUrl: 'invalid-url' };
      
      (chrome.storage.local.get as jest.Mock).mockResolvedValue({
        screenmonitor_config: invalidConfig
      });

      await configService.initialize();
      
      const config = configService.getConfig();
      expect(config.mcpServerUrl).toBe('http://localhost:7777');
      expect(mockLogger.warn).toHaveBeenCalled();
    });
  });

  describe('configuration updates', () => {
    beforeEach(async () => {
      await configService.initialize();
    });

    it('should update configuration successfully', async () => {
      const updates = { mcpServerUrl: 'http://localhost:8888' };
      
      const result = await configService.updateConfig(updates);
      
      expect(result.isValid).toBe(true);
      expect(configService.getConfig().mcpServerUrl).toBe('http://localhost:8888');
      expect(chrome.storage.local.set).toHaveBeenCalled();
    });

    it('should reject invalid configuration updates', async () => {
      const updates = { mcpServerUrl: 'invalid-url' };
      
      const result = await configService.updateConfig(updates);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('Invalid MCP server URL'));
    });

    it('should validate allowed domains', async () => {
      const updates = { allowedDomains: ['valid.com', 'invalid domain'] };
      
      const result = await configService.updateConfig(updates);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('domain management', () => {
    beforeEach(async () => {
      await configService.initialize();
    });

    it('should add valid domain', async () => {
      const result = await configService.addDomain('example.com');
      
      expect(result).toBe(true);
      expect(configService.getConfig().allowedDomains).toContain('example.com');
    });

    it('should reject invalid domain', async () => {
      const result = await configService.addDomain('invalid domain');
      
      expect(result).toBe(false);
      expect(configService.getConfig().allowedDomains).not.toContain('invalid domain');
    });

    it('should not add duplicate domain', async () => {
      await configService.addDomain('example.com');
      const result = await configService.addDomain('example.com');
      
      expect(result).toBe(true);
      const domains = configService.getConfig().allowedDomains;
      const count = domains.filter(d => d === 'example.com').length;
      expect(count).toBe(1);
    });

    it('should remove domain', async () => {
      await configService.addDomain('example.com');
      const result = await configService.removeDomain('example.com');
      
      expect(result).toBe(true);
      expect(configService.getConfig().allowedDomains).not.toContain('example.com');
    });

    it('should not remove essential domains', async () => {
      const result = await configService.removeDomain('localhost');
      
      expect(result).toBe(false);
      expect(configService.getConfig().allowedDomains).toContain('localhost');
    });

    it('should check if domain is allowed', () => {
      expect(configService.isDomainAllowed('localhost')).toBe(true);
      expect(configService.isDomainAllowed('unknown.com')).toBe(false);
    });

    it('should check subdomain permissions', async () => {
      await configService.addDomain('example.com');
      
      expect(configService.isDomainAllowed('sub.example.com')).toBe(true);
      expect(configService.isDomainAllowed('other.com')).toBe(false);
    });
  });

  describe('feature management', () => {
    beforeEach(async () => {
      await configService.initialize();
    });

    it('should toggle feature on', async () => {
      const result = await configService.toggleFeature('form_automation', true);
      
      expect(result).toBe(true);
      expect(configService.isFeatureEnabled('form_automation')).toBe(true);
    });

    it('should toggle feature off', async () => {
      const result = await configService.toggleFeature('dom_monitoring', false);
      
      expect(result).toBe(false);
      expect(configService.isFeatureEnabled('dom_monitoring')).toBe(false);
    });

    it('should toggle feature state', async () => {
      const initialState = configService.isFeatureEnabled('smart_click');
      const result = await configService.toggleFeature('smart_click');
      
      expect(result).toBe(!initialState);
      expect(configService.isFeatureEnabled('smart_click')).toBe(!initialState);
    });

    it('should check feature enabled status', () => {
      expect(configService.isFeatureEnabled('dom_monitoring')).toBe(true);
      expect(configService.isFeatureEnabled('form_automation')).toBe(false);
    });
  });

  describe('configuration validation', () => {
    it('should validate MCP server URL', async () => {
      const validUrls = [
        'http://localhost:7777',
        'https://example.com:8080',
        'http://127.0.0.1:3000'
      ];

      for (const url of validUrls) {
        const result = await configService.updateConfig({ mcpServerUrl: url });
        expect(result.isValid).toBe(true);
      }
    });

    it('should reject invalid MCP server URLs', async () => {
      const invalidUrls = [
        'invalid-url',
        'ftp://example.com',
        'not-a-url',
        ''
      ];

      for (const url of invalidUrls) {
        const result = await configService.updateConfig({ mcpServerUrl: url });
        expect(result.isValid).toBe(false);
      }
    });

    it('should validate enabled features', async () => {
      const validFeatures = ['dom_monitoring', 'smart_click', 'text_extraction'];
      const result = await configService.updateConfig({ enabledFeatures: validFeatures });
      
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid features', async () => {
      const invalidFeatures = ['invalid_feature', 'another_invalid'];
      const result = await configService.updateConfig({ enabledFeatures: invalidFeatures });
      
      expect(result.isValid).toBe(false);
    });
  });

  describe('configuration reset', () => {
    beforeEach(async () => {
      await configService.initialize();
    });

    it('should reset to defaults', async () => {
      // Modify configuration
      await configService.updateConfig({ mcpServerUrl: 'http://custom:8888' });
      await configService.addDomain('custom.com');
      
      // Reset to defaults
      await configService.resetToDefaults();
      
      const config = configService.getConfig();
      expect(config.mcpServerUrl).toBe('http://localhost:7777');
      expect(config.allowedDomains).not.toContain('custom.com');
    });
  });

  describe('error handling', () => {
    it('should handle storage errors gracefully', async () => {
      (chrome.storage.local.get as jest.Mock).mockRejectedValue(new Error('Storage error'));
      
      await expect(configService.initialize()).rejects.toThrow('Configuration initialization failed');
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle storage save errors', async () => {
      await configService.initialize();
      (chrome.storage.local.set as jest.Mock).mockRejectedValue(new Error('Save error'));
      
      const result = await configService.updateConfig({ debugMode: true });
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('Failed to update configuration'));
    });
  });

  describe('configuration retrieval', () => {
    beforeEach(async () => {
      await configService.initialize();
    });

    it('should get specific configuration value', () => {
      const mcpServerUrl = configService.get('mcpServerUrl');
      expect(mcpServerUrl).toBe('http://localhost:7777');
    });

    it('should return copy of configuration', () => {
      const config1 = configService.getConfig();
      const config2 = configService.getConfig();
      
      expect(config1).toEqual(config2);
      expect(config1).not.toBe(config2); // Different objects
    });
  });
});