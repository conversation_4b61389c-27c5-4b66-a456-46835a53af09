/**
 * ScreenMonitorMCP Browser Extension - Storage Service
 * 
 * Type-safe wrapper around Chrome Storage API with caching,
 * validation, and automatic serialization/deserialization.
 */

import type { Logger, StorageData } from '../types';

// ============================================================================
// Storage Service Configuration
// ============================================================================

interface StorageServiceConfig {
  readonly enableCache: boolean;
  readonly cacheTimeout: number;
  readonly enableValidation: boolean;
  readonly maxStorageSize: number;
}

const DEFAULT_CONFIG: StorageServiceConfig = {
  enableCache: true,
  cacheTimeout: 300000, // 5 minutes
  enableValidation: true,
  maxStorageSize: 5 * 1024 * 1024, // 5MB
};

// ============================================================================
// Cache Entry Interface
// ============================================================================

interface CacheEntry<T = unknown> {
  readonly data: T;
  readonly timestamp: number;
  readonly ttl: number;
}

// ============================================================================
// Storage Service
// ============================================================================

export class StorageService {
  private readonly config: StorageServiceConfig;
  private readonly logger: Logger;
  private readonly cache = new Map<string, CacheEntry>();
  private readonly changeListeners = new Map<string, Set<(value: unknown) => void>>();

  constructor(logger: Logger, config: Partial<StorageServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.logger = logger.createChild({ service: 'StorageService' });
    this.initializeStorageListener();
  }

  /**
   * Get value from storage
   */
  async get<T = unknown>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      this.logger.debug('Getting value from storage', { key });

      // Check cache first
      if (this.config.enableCache) {
        const cached = this.getFromCache<T>(key);
        if (cached !== undefined) {
          this.logger.debug('Value retrieved from cache', { key });
          return cached;
        }
      }

      // Get from Chrome storage
      const result = await chrome.storage.local.get(key);
      const value = result[key];

      if (value === undefined) {
        this.logger.debug('Value not found in storage', { key });
        return defaultValue;
      }

      // Validate if enabled
      if (this.config.enableValidation) {
        const validation = this.validateStorageValue(value);
        if (!validation.isValid) {
          this.logger.warn('Invalid value in storage', { key, errors: validation.errors });
          return defaultValue;
        }
      }

      // Cache the value
      if (this.config.enableCache) {
        this.setInCache(key, value);
      }

      this.logger.debug('Value retrieved from storage', { key });
      return value as T;
    } catch (error) {
      this.logger.error('Failed to get value from storage', error as Error, { key });
      return defaultValue;
    }
  }

  /**
   * Set value in storage
   */
  async set<T>(key: string, value: T): Promise<boolean> {
    try {
      this.logger.debug('Setting value in storage', { key });

      // Validate if enabled
      if (this.config.enableValidation) {
        const validation = this.validateStorageValue(value);
        if (!validation.isValid) {
          this.logger.warn('Invalid value for storage', { key, errors: validation.errors });
          return false;
        }
      }

      // Check storage size
      const serialized = JSON.stringify(value);
      if (serialized.length > this.config.maxStorageSize) {
        this.logger.warn('Value too large for storage', { 
          key, 
          size: serialized.length,
          maxSize: this.config.maxStorageSize
        });
        return false;
      }

      // Set in Chrome storage
      await chrome.storage.local.set({ [key]: value });

      // Update cache
      if (this.config.enableCache) {
        this.setInCache(key, value);
      }

      this.logger.debug('Value set in storage', { key });
      return true;
    } catch (error) {
      this.logger.error('Failed to set value in storage', error as Error, { key });
      return false;
    }
  }

  /**
   * Remove value from storage
   */
  async remove(key: string): Promise<boolean> {
    try {
      this.logger.debug('Removing value from storage', { key });

      await chrome.storage.local.remove(key);

      // Remove from cache
      if (this.config.enableCache) {
        this.cache.delete(key);
      }

      this.logger.debug('Value removed from storage', { key });
      return true;
    } catch (error) {
      this.logger.error('Failed to remove value from storage', error as Error, { key });
      return false;
    }
  }

  /**
   * Clear all storage
   */
  async clear(): Promise<boolean> {
    try {
      this.logger.info('Clearing all storage');

      await chrome.storage.local.clear();

      // Clear cache
      if (this.config.enableCache) {
        this.cache.clear();
      }

      this.logger.info('All storage cleared');
      return true;
    } catch (error) {
      this.logger.error('Failed to clear storage', error as Error);
      return false;
    }
  }

  /**
   * Get multiple values from storage
   */
  async getMultiple<T = Record<string, unknown>>(keys: string[]): Promise<Partial<T>> {
    try {
      this.logger.debug('Getting multiple values from storage', { keys });

      const result: Partial<T> = {};
      const uncachedKeys: string[] = [];

      // Check cache first
      if (this.config.enableCache) {
        for (const key of keys) {
          const cached = this.getFromCache(key);
          if (cached !== undefined) {
            (result as any)[key] = cached;
          } else {
            uncachedKeys.push(key);
          }
        }
      } else {
        uncachedKeys.push(...keys);
      }

      // Get uncached values from storage
      if (uncachedKeys.length > 0) {
        const storageResult = await chrome.storage.local.get(uncachedKeys);
        
        for (const key of uncachedKeys) {
          const value = storageResult[key];
          if (value !== undefined) {
            (result as any)[key] = value;
            
            // Cache the value
            if (this.config.enableCache) {
              this.setInCache(key, value);
            }
          }
        }
      }

      this.logger.debug('Multiple values retrieved from storage', { 
        keys, 
        foundKeys: Object.keys(result) 
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to get multiple values from storage', error as Error, { keys });
      return {};
    }
  }

  /**
   * Set multiple values in storage
   */
  async setMultiple<T = Record<string, unknown>>(data: T): Promise<boolean> {
    try {
      this.logger.debug('Setting multiple values in storage', { 
        keys: Object.keys(data as object) 
      });

      // Validate all values if enabled
      if (this.config.enableValidation) {
        for (const [key, value] of Object.entries(data as object)) {
          const validation = this.validateStorageValue(value);
          if (!validation.isValid) {
            this.logger.warn('Invalid value in batch set', { 
              key, 
              errors: validation.errors 
            });
            return false;
          }
        }
      }

      // Set in Chrome storage
      await chrome.storage.local.set(data as Record<string, unknown>);

      // Update cache
      if (this.config.enableCache) {
        for (const [key, value] of Object.entries(data as object)) {
          this.setInCache(key, value);
        }
      }

      this.logger.debug('Multiple values set in storage', { 
        keys: Object.keys(data as object) 
      });
      return true;
    } catch (error) {
      this.logger.error('Failed to set multiple values in storage', error as Error);
      return false;
    }
  }

  /**
   * Listen for changes to a specific key
   */
  addChangeListener(key: string, callback: (value: unknown) => void): void {
    if (!this.changeListeners.has(key)) {
      this.changeListeners.set(key, new Set());
    }
    
    this.changeListeners.get(key)!.add(callback);
    this.logger.debug('Change listener added', { key });
  }

  /**
   * Remove change listener
   */
  removeChangeListener(key: string, callback: (value: unknown) => void): void {
    const listeners = this.changeListeners.get(key);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.changeListeners.delete(key);
      }
      this.logger.debug('Change listener removed', { key });
    }
  }

  /**
   * Get storage usage information
   */
  async getUsage(): Promise<{
    bytesInUse: number;
    quotaBytes: number;
    percentUsed: number;
  }> {
    try {
      const bytesInUse = await chrome.storage.local.getBytesInUse();
      const quotaBytes = chrome.storage.local.QUOTA_BYTES;
      const percentUsed = (bytesInUse / quotaBytes) * 100;

      return {
        bytesInUse,
        quotaBytes,
        percentUsed: Math.round(percentUsed * 100) / 100,
      };
    } catch (error) {
      this.logger.error('Failed to get storage usage', error as Error);
      return {
        bytesInUse: 0,
        quotaBytes: chrome.storage.local.QUOTA_BYTES,
        percentUsed: 0,
      };
    }
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): number {
    if (!this.config.enableCache) {
      return 0;
    }

    const now = Date.now();
    let cleared = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleared++;
      }
    }

    if (cleared > 0) {
      this.logger.debug('Expired cache entries cleared', { count: cleared });
    }

    return cleared;
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    hitRate: number;
    entries: Array<{ key: string; age: number; size: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      age: Date.now() - entry.timestamp,
      size: JSON.stringify(entry.data).length,
    }));

    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses
      entries,
    };
  }

  // ============================================================================
  // Private Methods
  // ============================================================================

  private getFromCache<T>(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) {
      return undefined;
    }

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    return entry.data as T;
  }

  private setInCache<T>(key: string, value: T): void {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl: this.config.cacheTimeout,
    });
  }

  private validateStorageValue(value: unknown): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // Test serialization
      JSON.stringify(value);
    } catch (error) {
      errors.push('Value is not serializable');
    }

    // Check for functions (not serializable)
    if (typeof value === 'function') {
      errors.push('Functions cannot be stored');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private initializeStorageListener(): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName !== 'local') {
        return;
      }

      for (const [key, change] of Object.entries(changes)) {
        // Update cache
        if (this.config.enableCache && change.newValue !== undefined) {
          this.setInCache(key, change.newValue);
        } else if (this.config.enableCache && change.newValue === undefined) {
          this.cache.delete(key);
        }

        // Notify listeners
        const listeners = this.changeListeners.get(key);
        if (listeners) {
          for (const callback of listeners) {
            try {
              callback(change.newValue);
            } catch (error) {
              this.logger.error('Storage change listener error', error as Error, { key });
            }
          }
        }

        this.logger.debug('Storage changed', { 
          key, 
          hasNewValue: change.newValue !== undefined,
          hasOldValue: change.oldValue !== undefined
        });
      }
    });

    this.logger.debug('Storage listener initialized');
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.cache.clear();
    this.changeListeners.clear();
    this.logger.debug('Storage service cleaned up');
  }
}