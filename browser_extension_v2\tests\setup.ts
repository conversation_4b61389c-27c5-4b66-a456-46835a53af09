/**
 * ScreenMonitorMCP Browser Extension - Test Setup
 * 
 * Test environment setup and utilities for browser extension testing.
 */

// ============================================================================
// Mock Chrome APIs
// ============================================================================

interface MockChromeStorage {
  local: {
    get: jest.Mock;
    set: jest.Mock;
    remove: jest.Mock;
    clear: jest.Mock;
    getBytesInUse: jest.Mock;
    onChanged: {
      addListener: jest.Mock;
      removeListener: jest.Mock;
    };
    QUOTA_BYTES: number;
  };
}

interface MockChromeRuntime {
  sendMessage: jest.Mock;
  onMessage: {
    addListener: jest.Mock;
    removeListener: jest.Mock;
  };
  getManifest: jest.Mock;
  onStartup: {
    addListener: jest.Mock;
  };
  onInstalled: {
    addListener: jest.Mock;
  };
}

interface MockChromeTabs {
  query: jest.Mock;
  get: jest.Mock;
  sendMessage: jest.Mock;
  onActivated: {
    addListener: jest.Mock;
  };
  onUpdated: {
    addListener: jest.Mock;
  };
  onRemoved: {
    addListener: jest.Mock;
  };
}

interface MockChrome {
  storage: MockChromeStorage;
  runtime: MockChromeRuntime;
  tabs: MockChromeTabs;
}

// ============================================================================
// Chrome API Mocks
// ============================================================================

export function createMockChrome(): MockChrome {
  const mockStorage: MockChromeStorage = {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(undefined),
      remove: jest.fn().mockResolvedValue(undefined),
      clear: jest.fn().mockResolvedValue(undefined),
      getBytesInUse: jest.fn().mockResolvedValue(0),
      onChanged: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
      QUOTA_BYTES: 5242880, // 5MB
    },
  };

  const mockRuntime: MockChromeRuntime = {
    sendMessage: jest.fn().mockResolvedValue({ success: true }),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
    },
    getManifest: jest.fn().mockReturnValue({
      version: '2.0.0',
      name: 'ScreenMonitorMCP Test Extension',
    }),
    onStartup: {
      addListener: jest.fn(),
    },
    onInstalled: {
      addListener: jest.fn(),
    },
  };

  const mockTabs: MockChromeTabs = {
    query: jest.fn().mockResolvedValue([]),
    get: jest.fn().mockResolvedValue({
      id: 1,
      url: 'http://localhost:3000',
      title: 'Test Page',
    }),
    sendMessage: jest.fn().mockResolvedValue({ success: true }),
    onActivated: {
      addListener: jest.fn(),
    },
    onUpdated: {
      addListener: jest.fn(),
    },
    onRemoved: {
      addListener: jest.fn(),
    },
  };

  return {
    storage: mockStorage,
    runtime: mockRuntime,
    tabs: mockTabs,
  };
}

// ============================================================================
// DOM Mocks
// ============================================================================

export function createMockDOM() {
  // Mock document
  const mockDocument = {
    readyState: 'complete',
    title: 'Test Page',
    URL: 'http://localhost:3000/test',
    querySelector: jest.fn(),
    querySelectorAll: jest.fn().mockReturnValue([]),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    createElement: jest.fn(),
    body: {
      appendChild: jest.fn(),
      removeChild: jest.fn(),
    },
  };

  // Mock window
  const mockWindow = {
    location: {
      href: 'http://localhost:3000/test',
      hostname: 'localhost',
      pathname: '/test',
    },
    innerWidth: 1920,
    innerHeight: 1080,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    postMessage: jest.fn(),
  };

  // Mock MutationObserver
  const mockMutationObserver = jest.fn().mockImplementation((callback) => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
    takeRecords: jest.fn().mockReturnValue([]),
  }));

  return {
    document: mockDocument,
    window: mockWindow,
    MutationObserver: mockMutationObserver,
  };
}

// ============================================================================
// Test Data Factories
// ============================================================================

export const TestDataFactory = {
  createExtensionConfig: () => ({
    mcpServerUrl: 'http://localhost:7777',
    allowedDomains: ['localhost', '127.0.0.1', 'file'],
    enabledFeatures: ['dom_monitoring', 'smart_click'],
    extensionEnabled: true,
    autoSyncDomains: true,
    debugMode: true,
    apiVersion: '2.1.0',
  }),

  createSessionInfo: (overrides = {}) => ({
    sessionId: 'test-session-123',
    domain: 'localhost',
    url: 'http://localhost:3000/test',
    tabId: 1,
    features: ['dom_monitoring', 'smart_click'],
    userAgent: 'Mozilla/5.0 (Test Browser)',
    registeredAt: new Date().toISOString(),
    lastActivity: new Date().toISOString(),
    ...overrides,
  }),

  createDOMEvent: (overrides = {}) => ({
    eventType: 'click',
    url: 'http://localhost:3000/test',
    timestamp: new Date().toISOString(),
    target: {
      tagName: 'BUTTON',
      id: 'test-button',
      className: 'btn btn-primary',
      textContent: 'Click me',
      selector: '#test-button',
    },
    coordinates: { x: 100, y: 200 },
    ...overrides,
  }),

  createSmartClickData: (overrides = {}) => ({
    description: 'Click the submit button',
    selector: '#submit-btn',
    confidence: 0.8,
    timeout: 5000,
    dryRun: false,
    ...overrides,
  }),

  createAPIResponse: (success = true, data = null, error = null) => ({
    success,
    data,
    error,
    timestamp: new Date().toISOString(),
    requestId: 'test-request-123',
  }),
};

// ============================================================================
// Test Utilities
// ============================================================================

export class TestUtils {
  /**
   * Wait for a specified amount of time
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Wait for a condition to be true
   */
  static async waitFor(
    condition: () => boolean | Promise<boolean>,
    timeout = 5000,
    interval = 100
  ): Promise<void> {
    const start = Date.now();
    
    while (Date.now() - start < timeout) {
      if (await condition()) {
        return;
      }
      await this.wait(interval);
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  }

  /**
   * Create a mock function that resolves after a delay
   */
  static createAsyncMock<T>(
    returnValue: T,
    delay = 0
  ): jest.Mock<Promise<T>, any[]> {
    return jest.fn().mockImplementation(async (...args) => {
      if (delay > 0) {
        await this.wait(delay);
      }
      return returnValue;
    });
  }

  /**
   * Create a mock function that rejects after a delay
   */
  static createAsyncErrorMock(
    error: Error,
    delay = 0
  ): jest.Mock<Promise<never>, any[]> {
    return jest.fn().mockImplementation(async (...args) => {
      if (delay > 0) {
        await this.wait(delay);
      }
      throw error;
    });
  }

  /**
   * Simulate a DOM element
   */
  static createMockElement(overrides = {}) {
    return {
      tagName: 'DIV',
      id: '',
      className: '',
      textContent: '',
      getAttribute: jest.fn(),
      setAttribute: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      click: jest.fn(),
      focus: jest.fn(),
      blur: jest.fn(),
      scrollIntoView: jest.fn(),
      getBoundingClientRect: jest.fn().mockReturnValue({
        x: 0,
        y: 0,
        width: 100,
        height: 50,
        top: 0,
        right: 100,
        bottom: 50,
        left: 0,
      }),
      ...overrides,
    };
  }

  /**
   * Simulate a mouse event
   */
  static createMouseEvent(type: string, overrides = {}) {
    return {
      type,
      bubbles: true,
      cancelable: true,
      clientX: 100,
      clientY: 200,
      target: this.createMockElement(),
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
      ...overrides,
    };
  }

  /**
   * Simulate a mutation record
   */
  static createMutationRecord(overrides = {}) {
    return {
      type: 'childList',
      target: this.createMockElement(),
      addedNodes: [],
      removedNodes: [],
      previousSibling: null,
      nextSibling: null,
      attributeName: null,
      attributeNamespace: null,
      oldValue: null,
      ...overrides,
    };
  }
}

// ============================================================================
// Test Environment Setup
// ============================================================================

export function setupTestEnvironment() {
  // Mock Chrome APIs
  const mockChrome = createMockChrome();
  (global as any).chrome = mockChrome;

  // Mock DOM APIs
  const mockDOM = createMockDOM();
  (global as any).document = mockDOM.document;
  (global as any).window = mockDOM.window;
  (global as any).MutationObserver = mockDOM.MutationObserver;

  // Mock crypto API
  const mockCrypto = {
    getRandomValues: jest.fn().mockImplementation((array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    }),
    subtle: {
      generateKey: jest.fn().mockResolvedValue({}),
      encrypt: jest.fn().mockResolvedValue(new ArrayBuffer(16)),
      decrypt: jest.fn().mockResolvedValue(new ArrayBuffer(16)),
    },
  };
  (global as any).crypto = mockCrypto;

  // Mock console methods for cleaner test output
  const originalConsole = global.console;
  global.console = {
    ...originalConsole,
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };

  // Return cleanup function
  return () => {
    global.console = originalConsole;
    delete (global as any).chrome;
    delete (global as any).document;
    delete (global as any).window;
    delete (global as any).MutationObserver;
    delete (global as any).crypto;
  };
}

// ============================================================================
// Custom Matchers
// ============================================================================

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidSessionId(): R;
      toBeValidURL(): R;
      toBeValidDomain(): R;
      toHaveBeenCalledWithMessage(messageType: string): R;
    }
  }
}

export function setupCustomMatchers() {
  expect.extend({
    toBeValidSessionId(received: string) {
      const sessionIdRegex = /^[a-zA-Z0-9_-]{10,50}$/;
      const pass = sessionIdRegex.test(received);
      
      return {
        message: () =>
          `expected ${received} ${pass ? 'not ' : ''}to be a valid session ID`,
        pass,
      };
    },

    toBeValidURL(received: string) {
      try {
        new URL(received);
        return {
          message: () => `expected ${received} not to be a valid URL`,
          pass: true,
        };
      } catch {
        return {
          message: () => `expected ${received} to be a valid URL`,
          pass: false,
        };
      }
    },

    toBeValidDomain(received: string) {
      const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
      const pass = domainRegex.test(received) || ['localhost', '127.0.0.1', 'file'].includes(received);
      
      return {
        message: () =>
          `expected ${received} ${pass ? 'not ' : ''}to be a valid domain`,
        pass,
      };
    },

    toHaveBeenCalledWithMessage(received: jest.Mock, messageType: string) {
      const calls = received.mock.calls;
      const pass = calls.some(call => 
        call[0] && typeof call[0] === 'object' && call[0].type === messageType
      );
      
      return {
        message: () =>
          `expected mock to ${pass ? 'not ' : ''}have been called with message type "${messageType}"`,
        pass,
      };
    },
  });
}

// ============================================================================
// Export All Test Utilities
// ============================================================================

export {
  createMockChrome,
  createMockDOM,
};

export default {
  setupTestEnvironment,
  setupCustomMatchers,
  TestDataFactory,
  TestUtils,
  createMockChrome,
  createMockDOM,
};