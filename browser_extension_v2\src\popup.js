/**
 * ScreenMonitorMCP Extension Popup Script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Popup loaded');
    
    // Test button functionality - Test MCP Server connection
    document.getElementById('testBtn').addEventListener('click', function() {
        console.log('Testing MCP Server connection...');

        // Show loading state
        const testBtn = document.getElementById('testBtn');
        const originalText = testBtn.textContent;
        testBtn.textContent = 'Testing...';
        testBtn.disabled = true;

        // Send message to background script to test MCP connection
        chrome.runtime.sendMessage({
            type: 'TEST_CONNECTION',
            timestamp: new Date().toISOString()
        }, function(response) {
            console.log('MCP Server response:', response);

            // Reset button
            testBtn.textContent = originalText;
            testBtn.disabled = false;

            if (response && response.success) {
                alert(`✅ MCP Server Connection Successful!\n\n${response.data}\n\nServer Info: ${JSON.stringify(response.serverInfo, null, 2)}`);
                updateStatus(true);
            } else {
                alert(`❌ MCP Server Connection Failed!\n\nError: ${response.error}`);
                updateStatus(false);
            }
        });
    });
    
    // Settings button functionality
    document.getElementById('settingsBtn').addEventListener('click', function() {
        console.log('Settings button clicked');
        alert('Settings panel coming soon!');
    });
    
    // Update status
    updateStatus();
});

function updateStatus(mcpConnected = null) {
    const statusDiv = document.getElementById('status');

    if (mcpConnected === true) {
        statusDiv.className = 'status active';
        statusDiv.textContent = '✅ MCP Server Connected';
    } else if (mcpConnected === false) {
        statusDiv.className = 'status inactive';
        statusDiv.textContent = '❌ MCP Server Disconnected';
    } else {
        // Check if extension is working
        chrome.runtime.sendMessage({
            type: 'STATUS_CHECK'
        }, function(response) {
            if (chrome.runtime.lastError) {
                statusDiv.className = 'status inactive';
                statusDiv.textContent = '❌ Extension Inactive';
            } else {
                statusDiv.className = 'status active';
                statusDiv.textContent = '✅ Extension Active';
            }
        });
    }
}
