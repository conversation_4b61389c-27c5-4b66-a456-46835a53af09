/**
 * ScreenMonitorMCP Extension Popup Script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Popup loaded');
    
    // Test button functionality
    document.getElementById('testBtn').addEventListener('click', function() {
        console.log('Test button clicked');
        
        // Send message to background script
        chrome.runtime.sendMessage({
            type: 'TEST_CONNECTION',
            timestamp: new Date().toISOString()
        }, function(response) {
            console.log('Background response:', response);
            
            if (response && response.success) {
                alert('✅ Extension is working!\n\nBackground script responded successfully.');
            } else {
                alert('❌ Extension test failed');
            }
        });
    });
    
    // Settings button functionality
    document.getElementById('settingsBtn').addEventListener('click', function() {
        console.log('Settings button clicked');
        alert('Settings panel coming soon!');
    });
    
    // Update status
    updateStatus();
});

function updateStatus() {
    const statusDiv = document.getElementById('status');
    
    // Check if extension is working
    chrome.runtime.sendMessage({
        type: 'STATUS_CHECK'
    }, function(response) {
        if (chrome.runtime.lastError) {
            statusDiv.className = 'status inactive';
            statusDiv.textContent = '❌ Extension Inactive';
        } else {
            statusDiv.className = 'status active';
            statusDiv.textContent = '✅ Extension Active';
        }
    });
}
