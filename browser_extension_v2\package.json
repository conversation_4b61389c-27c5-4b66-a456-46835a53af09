{"name": "screenmonitor-mcp-extension-v2", "version": "2.0.0", "description": "Modern browser extension for ScreenMonitorMCP with TypeScript and clean architecture", "main": "dist/background.js", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "watch": "webpack --mode=development --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run clean && npm run build:dev && npm run watch"}, "keywords": ["browser-extension", "chrome-extension", "typescript", "mcp", "screen-monitor", "dom-monitoring", "smart-click"], "author": "ScreenMonitorMCP Team", "license": "MIT", "devDependencies": {"@types/chrome": "^0.0.246", "@types/jest": "^29.5.5", "@types/node": "^20.6.3", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.1", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "dependencies": {}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"], "testMatch": ["<rootDir>/tests/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/types/**/*"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}}, "eslintConfig": {"extends": ["@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}, "env": {"browser": true, "es2021": true, "jest": true}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}, "browserslist": ["last 2 Chrome versions", "last 2 Firefox versions", "last 2 Edge versions"]}