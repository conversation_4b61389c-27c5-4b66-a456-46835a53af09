/**
 * ScreenMonitorMCP Browser Extension - Message Service
 * 
 * Secure message passing service with validation, error handling,
 * and type-safe communication between extension components.
 */

import type { 
  ExtensionMessage, 
  MessageHandler, 
  Logger, 
  APIResponse,
  ExtensionError,
  ErrorCode 
} from '../types';

// ============================================================================
// Message Service Configuration
// ============================================================================

interface MessageServiceConfig {
  readonly timeout: number;
  readonly retryAttempts: number;
  readonly retryDelay: number;
  readonly enableValidation: boolean;
}

const DEFAULT_CONFIG: MessageServiceConfig = {
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  enableValidation: true,
};

// ============================================================================
// Message Service
// ============================================================================

export class MessageService {
  private readonly config: MessageServiceConfig;
  private readonly logger: Logger;
  private readonly handlers = new Map<string, MessageHandler[]>();
  private readonly pendingRequests = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();

  constructor(logger: Logger, config: Partial<MessageServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.logger = logger.createChild({ service: 'MessageService' });
    this.initializeMessageListener();
  }

  /**
   * Send message to background script
   */
  async sendToBackground<T = unknown>(
    message: Omit<ExtensionMessage, 'timestamp'>
  ): Promise<T> {
    return this.sendMessage(message, chrome.runtime.sendMessage.bind(chrome.runtime));
  }

  /**
   * Send message to content script
   */
  async sendToContentScript<T = unknown>(
    tabId: number,
    message: Omit<ExtensionMessage, 'timestamp'>
  ): Promise<T> {
    return this.sendMessage(
      message, 
      (msg: ExtensionMessage) => chrome.tabs.sendMessage(tabId, msg)
    );
  }

  /**
   * Send message to all content scripts
   */
  async broadcastToContentScripts<T = unknown>(
    message: Omit<ExtensionMessage, 'timestamp'>
  ): Promise<T[]> {
    try {
      const tabs = await chrome.tabs.query({});
      const promises = tabs
        .filter(tab => tab.id !== undefined)
        .map(tab => 
          this.sendToContentScript(tab.id!, message).catch(error => {
            this.logger.debug('Failed to send to tab', { tabId: tab.id, error });
            return null;
          })
        );

      const results = await Promise.all(promises);
      return results.filter(result => result !== null);
    } catch (error) {
      this.logger.error('Failed to broadcast to content scripts', error as Error);
      throw this.createError('NETWORK_ERROR', 'Broadcast failed');
    }
  }

  /**
   * Register message handler
   */
  addHandler<T extends ExtensionMessage = ExtensionMessage>(
    messageType: string,
    handler: MessageHandler<T>
  ): void {
    if (!this.handlers.has(messageType)) {
      this.handlers.set(messageType, []);
    }
    
    this.handlers.get(messageType)!.push(handler as MessageHandler);
    this.logger.debug('Message handler registered', { messageType });
  }

  /**
   * Remove message handler
   */
  removeHandler(messageType: string, handler: MessageHandler): void {
    const handlers = this.handlers.get(messageType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        this.logger.debug('Message handler removed', { messageType });
      }
    }
  }

  /**
   * Remove all handlers for a message type
   */
  removeAllHandlers(messageType: string): void {
    this.handlers.delete(messageType);
    this.logger.debug('All handlers removed', { messageType });
  }

  /**
   * Create a response wrapper
   */
  createResponse<T = unknown>(
    success: boolean,
    data?: T,
    error?: string
  ): APIResponse<T> {
    return {
      success,
      data,
      error,
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
    };
  }

  /**
   * Create error response
   */
  createErrorResponse(code: ErrorCode, message: string): APIResponse {
    return this.createResponse(false, undefined, `${code}: ${message}`);
  }

  // ============================================================================
  // Private Methods
  // ============================================================================

  private async sendMessage<T>(
    message: Omit<ExtensionMessage, 'timestamp'>,
    sendFunction: (message: ExtensionMessage) => Promise<T>
  ): Promise<T> {
    const fullMessage: ExtensionMessage = {
      ...message,
      timestamp: new Date().toISOString(),
    };

    if (this.config.enableValidation) {
      const validation = this.validateMessage(fullMessage);
      if (!validation.isValid) {
        throw this.createError('VALIDATION_ERROR', validation.errors.join(', '));
      }
    }

    return this.sendWithRetry(fullMessage, sendFunction);
  }

  private async sendWithRetry<T>(
    message: ExtensionMessage,
    sendFunction: (message: ExtensionMessage) => Promise<T>
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.retryAttempts; attempt++) {
      try {
        this.logger.debug('Sending message', { 
          type: message.type, 
          attempt: attempt + 1,
          maxAttempts: this.config.retryAttempts + 1
        });

        const result = await this.sendWithTimeout(message, sendFunction);
        
        if (attempt > 0) {
          this.logger.info('Message sent successfully after retry', { 
            type: message.type, 
            attempts: attempt + 1 
          });
        }

        return result;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retryAttempts) {
          this.logger.warn('Message send failed, retrying', { 
            type: message.type, 
            attempt: attempt + 1,
            error: lastError.message
          });
          
          await this.delay(this.config.retryDelay * (attempt + 1));
        }
      }
    }

    this.logger.error('Message send failed after all retries', lastError!, { 
      type: message.type,
      attempts: this.config.retryAttempts + 1
    });

    throw this.createError('NETWORK_ERROR', `Failed to send message: ${lastError?.message}`);
  }

  private async sendWithTimeout<T>(
    message: ExtensionMessage,
    sendFunction: (message: ExtensionMessage) => Promise<T>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(this.createError('NETWORK_ERROR', 'Message timeout'));
      }, this.config.timeout);

      sendFunction(message)
        .then(result => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  private initializeMessageListener(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleIncomingMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    this.logger.debug('Message listener initialized');
  }

  private async handleIncomingMessage(
    message: ExtensionMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      this.logger.debug('Message received', { 
        type: message.type, 
        from: sender.tab ? 'content-script' : 'extension'
      });

      if (this.config.enableValidation) {
        const validation = this.validateMessage(message);
        if (!validation.isValid) {
          const errorResponse = this.createErrorResponse(
            'VALIDATION_ERROR', 
            validation.errors.join(', ')
          );
          sendResponse(errorResponse);
          return;
        }
      }

      const handlers = this.handlers.get(message.type) || [];
      
      if (handlers.length === 0) {
        this.logger.warn('No handlers found for message type', { type: message.type });
        sendResponse(this.createErrorResponse('UNKNOWN_ERROR', 'No handler found'));
        return;
      }

      // Execute handlers sequentially
      for (const handler of handlers) {
        try {
          const result = await handler(message, sender, sendResponse);
          if (result !== undefined) {
            sendResponse(this.createResponse(true, result));
            return;
          }
        } catch (error) {
          this.logger.error('Message handler error', error as Error, { 
            type: message.type 
          });
          sendResponse(this.createErrorResponse(
            'UNKNOWN_ERROR', 
            (error as Error).message
          ));
          return;
        }
      }

      // If no handler returned a result
      sendResponse(this.createResponse(true));
    } catch (error) {
      this.logger.error('Message handling error', error as Error);
      sendResponse(this.createErrorResponse('UNKNOWN_ERROR', 'Internal error'));
    }
  }

  private validateMessage(message: ExtensionMessage): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!message.type || typeof message.type !== 'string') {
      errors.push('Message type is required and must be a string');
    }

    if (!message.timestamp || typeof message.timestamp !== 'string') {
      errors.push('Message timestamp is required and must be a string');
    } else {
      try {
        new Date(message.timestamp);
      } catch {
        errors.push('Message timestamp must be a valid ISO string');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private createError(code: ErrorCode, message: string): ExtensionError {
    return {
      code,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get service statistics
   */
  getStats(): {
    registeredHandlers: number;
    handlersByType: Record<string, number>;
    pendingRequests: number;
  } {
    const handlersByType: Record<string, number> = {};
    
    for (const [type, handlers] of this.handlers.entries()) {
      handlersByType[type] = handlers.length;
    }

    return {
      registeredHandlers: Array.from(this.handlers.values()).reduce(
        (total, handlers) => total + handlers.length, 
        0
      ),
      handlersByType,
      pendingRequests: this.pendingRequests.size,
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.handlers.clear();
    
    // Clear pending requests
    for (const [id, request] of this.pendingRequests.entries()) {
      clearTimeout(request.timeout);
      request.reject(this.createError('UNKNOWN_ERROR', 'Service cleanup'));
    }
    this.pendingRequests.clear();

    this.logger.debug('Message service cleaned up');
  }
}