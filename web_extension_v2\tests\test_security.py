"""
Tests for web extension security module
"""

import pytest
import time
from unittest.mock import Mock, patch
from web_extension_v2.security import (
    InputValidator,
    RateLimiter,
    CSRFProtection,
    SessionSecurity,
    SecurityManager,
    SecurityConfig,
    validate_input,
    rate_limit,
    require_valid_session
)

class TestInputValidator:
    """Test input validation functionality"""
    
    def test_validate_domain_valid(self):
        """Test valid domain validation"""
        valid_domains = [
            'localhost',
            '127.0.0.1',
            'file',
            'example.com',
            'sub.example.com',
            'test-domain.co.uk'
        ]
        
        for domain in valid_domains:
            is_valid, error = InputValidator.validate_domain(domain)
            assert is_valid, f"Domain {domain} should be valid, got error: {error}"
    
    def test_validate_domain_invalid(self):
        """Test invalid domain validation"""
        invalid_domains = [
            '',
            None,
            123,
            'invalid domain',
            'domain with spaces',
            'a' * 300,  # Too long
            '.invalid',
            'invalid.',
            '<EMAIL>'
        ]
        
        for domain in invalid_domains:
            is_valid, error = InputValidator.validate_domain(domain)
            assert not is_valid, f"Domain {domain} should be invalid"
            assert error is not None
    
    def test_validate_session_id_valid(self):
        """Test valid session ID validation"""
        valid_session_ids = [
            'session_123456789',
            'test-session-id',
            'abcdefghij1234567890',
            'session_' + 'a' * 40
        ]
        
        for session_id in valid_session_ids:
            is_valid, error = InputValidator.validate_session_id(session_id)
            assert is_valid, f"Session ID {session_id} should be valid, got error: {error}"
    
    def test_validate_session_id_invalid(self):
        """Test invalid session ID validation"""
        invalid_session_ids = [
            '',
            None,
            123,
            'short',
            'session with spaces',
            'session@invalid',
            'a' * 100,  # Too long
            'session_id!'
        ]
        
        for session_id in invalid_session_ids:
            is_valid, error = InputValidator.validate_session_id(session_id)
            assert not is_valid, f"Session ID {session_id} should be invalid"
            assert error is not None
    
    def test_validate_url_valid(self):
        """Test valid URL validation"""
        valid_urls = [
            'http://localhost:7777',
            'https://example.com',
            'http://127.0.0.1:3000',
            'file:///path/to/file.html',
            'https://sub.example.com/path?query=value'
        ]
        
        for url in valid_urls:
            is_valid, error = InputValidator.validate_url(url)
            assert is_valid, f"URL {url} should be valid, got error: {error}"
    
    def test_validate_url_invalid(self):
        """Test invalid URL validation"""
        invalid_urls = [
            '',
            None,
            123,
            'not-a-url',
            'ftp://example.com',
            'javascript:alert(1)',
            'http://example.com<script>alert(1)</script>'
        ]
        
        for url in invalid_urls:
            is_valid, error = InputValidator.validate_url(url)
            assert not is_valid, f"URL {url} should be invalid"
            assert error is not None
    
    def test_validate_css_selector_valid(self):
        """Test valid CSS selector validation"""
        valid_selectors = [
            '#button-id',
            '.class-name',
            'div.container',
            'input[type="text"]',
            'div > p',
            'a:hover',
            '.btn.btn-primary'
        ]
        
        for selector in valid_selectors:
            is_valid, error = InputValidator.validate_css_selector(selector)
            assert is_valid, f"CSS selector {selector} should be valid, got error: {error}"
    
    def test_validate_css_selector_invalid(self):
        """Test invalid CSS selector validation"""
        invalid_selectors = [
            '',
            None,
            123,
            'javascript:alert(1)',
            '<script>alert(1)</script>',
            'selector with <iframe>'
        ]
        
        for selector in invalid_selectors:
            is_valid, error = InputValidator.validate_css_selector(selector)
            assert not is_valid, f"CSS selector {selector} should be invalid"
            assert error is not None
    
    def test_validate_features_valid(self):
        """Test valid features validation"""
        valid_features = [
            ['dom_monitoring'],
            ['smart_click', 'text_extraction'],
            ['dom_monitoring', 'smart_click', 'event_analysis']
        ]
        
        for features in valid_features:
            is_valid, error = InputValidator.validate_features(features)
            assert is_valid, f"Features {features} should be valid, got error: {error}"
    
    def test_validate_features_invalid(self):
        """Test invalid features validation"""
        invalid_features = [
            'not_a_list',
            ['invalid_feature'],
            ['dom_monitoring', 'invalid_feature'],
            [123, 'dom_monitoring'],
            []  # Empty list is technically valid but might want to warn
        ]
        
        for features in invalid_features[:-1]:  # Skip empty list
            is_valid, error = InputValidator.validate_features(features)
            assert not is_valid, f"Features {features} should be invalid"
            assert error is not None
    
    def test_contains_xss(self):
        """Test XSS pattern detection"""
        xss_patterns = [
            '<script>alert(1)</script>',
            'javascript:alert(1)',
            'onclick="alert(1)"',
            '<iframe src="evil.com"></iframe>',
            'data:text/html,<script>alert(1)</script>'
        ]
        
        for pattern in xss_patterns:
            assert InputValidator.contains_xss(pattern), f"Should detect XSS in: {pattern}"
    
    def test_sanitize_string(self):
        """Test string sanitization"""
        test_cases = [
            ('<script>alert(1)</script>', '&lt;&gt;alert(1)&lt;&#x2F;&gt;'),
            ('Hello & World', 'Hello &amp; World'),
            ('"quoted"', '&quot;quoted&quot;'),
            ("'single'", '&#x27;single&#x27;'),
            ('Path/to/file', 'Path&#x2F;to&#x2F;file')
        ]
        
        for input_str, expected in test_cases:
            result = InputValidator.sanitize_string(input_str)
            assert expected in result, f"Sanitization failed for: {input_str}"
    
    def test_validate_request_data(self):
        """Test complete request data validation"""
        valid_data = {
            'domain': 'localhost',
            'session_id': 'test_session_123',
            'url': 'http://localhost:3000',
            'features': ['dom_monitoring', 'smart_click'],
            'css_selector': '#button'
        }
        
        is_valid, errors = InputValidator.validate_request_data(valid_data)
        assert is_valid, f"Valid data should pass validation, got errors: {errors}"
        assert len(errors) == 0
        
        invalid_data = {
            'domain': 'invalid domain',
            'session_id': 'short',
            'url': 'not-a-url',
            'features': ['invalid_feature'],
            'css_selector': '<script>alert(1)</script>'
        }
        
        is_valid, errors = InputValidator.validate_request_data(invalid_data)
        assert not is_valid, "Invalid data should fail validation"
        assert len(errors) > 0


class TestRateLimiter:
    """Test rate limiting functionality"""
    
    def test_rate_limiter_allows_requests(self):
        """Test that rate limiter allows requests under limit"""
        limiter = RateLimiter(max_requests=5, window_seconds=60)
        
        for i in range(5):
            assert limiter.is_allowed('test_user'), f"Request {i+1} should be allowed"
    
    def test_rate_limiter_blocks_excess_requests(self):
        """Test that rate limiter blocks requests over limit"""
        limiter = RateLimiter(max_requests=3, window_seconds=60)
        
        # Use up the limit
        for i in range(3):
            assert limiter.is_allowed('test_user')
        
        # Next request should be blocked
        assert not limiter.is_allowed('test_user'), "Request over limit should be blocked"
    
    def test_rate_limiter_window_reset(self):
        """Test that rate limiter resets after window expires"""
        limiter = RateLimiter(max_requests=2, window_seconds=1)
        
        # Use up the limit
        assert limiter.is_allowed('test_user')
        assert limiter.is_allowed('test_user')
        assert not limiter.is_allowed('test_user')
        
        # Wait for window to expire
        time.sleep(1.1)
        
        # Should be allowed again
        assert limiter.is_allowed('test_user'), "Should be allowed after window reset"
    
    def test_rate_limiter_per_identifier(self):
        """Test that rate limiter works per identifier"""
        limiter = RateLimiter(max_requests=2, window_seconds=60)
        
        # User 1 uses up their limit
        assert limiter.is_allowed('user1')
        assert limiter.is_allowed('user1')
        assert not limiter.is_allowed('user1')
        
        # User 2 should still be allowed
        assert limiter.is_allowed('user2'), "Different user should be allowed"
        assert limiter.is_allowed('user2')
        assert not limiter.is_allowed('user2')
    
    def test_get_remaining_requests(self):
        """Test getting remaining request count"""
        limiter = RateLimiter(max_requests=5, window_seconds=60)
        
        assert limiter.get_remaining_requests('test_user') == 5
        
        limiter.is_allowed('test_user')
        assert limiter.get_remaining_requests('test_user') == 4
        
        limiter.is_allowed('test_user')
        assert limiter.get_remaining_requests('test_user') == 3
    
    def test_reset_rate_limiter(self):
        """Test resetting rate limiter"""
        limiter = RateLimiter(max_requests=2, window_seconds=60)
        
        # Use up the limit
        limiter.is_allowed('test_user')
        limiter.is_allowed('test_user')
        assert not limiter.is_allowed('test_user')
        
        # Reset specific user
        limiter.reset('test_user')
        assert limiter.is_allowed('test_user'), "Should be allowed after reset"
        
        # Reset all
        limiter.reset()
        assert limiter.get_remaining_requests('test_user') == 2


class TestCSRFProtection:
    """Test CSRF protection functionality"""
    
    def test_generate_and_validate_token(self):
        """Test CSRF token generation and validation"""
        csrf = CSRFProtection()
        
        token = csrf.generate_token('test_session')
        assert token is not None
        assert len(token) > 0
        
        assert csrf.validate_token('test_session', token), "Valid token should be accepted"
        assert not csrf.validate_token('test_session', 'invalid_token'), "Invalid token should be rejected"
        assert not csrf.validate_token('other_session', token), "Token for different session should be rejected"
    
    def test_token_expiration(self):
        """Test CSRF token expiration"""
        csrf = CSRFProtection()
        csrf.token_lifetime = 1  # 1 second for testing
        
        token = csrf.generate_token('test_session')
        assert csrf.validate_token('test_session', token), "Fresh token should be valid"
        
        time.sleep(1.1)
        assert not csrf.validate_token('test_session', token), "Expired token should be invalid"
    
    def test_cleanup_expired_tokens(self):
        """Test cleanup of expired tokens"""
        csrf = CSRFProtection()
        csrf.token_lifetime = 1
        
        # Generate some tokens
        csrf.generate_token('session1')
        csrf.generate_token('session2')
        
        assert len(csrf.tokens) == 2
        
        time.sleep(1.1)
        csrf.cleanup_expired_tokens()
        
        assert len(csrf.tokens) == 0, "Expired tokens should be cleaned up"


class TestSessionSecurity:
    """Test session security functionality"""
    
    def test_create_and_get_session(self):
        """Test session creation and retrieval"""
        session_mgr = SessionSecurity(timeout_seconds=60)
        
        session_data = {'user_id': 123, 'domain': 'localhost'}
        session_mgr.create_session('test_session', session_data)
        
        retrieved = session_mgr.get_session('test_session')
        assert retrieved is not None
        assert retrieved['user_id'] == 123
        assert retrieved['domain'] == 'localhost'
        assert 'created_at' in retrieved
        assert 'last_activity' in retrieved
    
    def test_session_validation(self):
        """Test session validation"""
        session_mgr = SessionSecurity(timeout_seconds=60)
        
        session_mgr.create_session('test_session', {})
        
        assert session_mgr.is_session_valid('test_session'), "Fresh session should be valid"
        assert not session_mgr.is_session_valid('nonexistent'), "Nonexistent session should be invalid"
    
    def test_session_activity_update(self):
        """Test session activity updates"""
        session_mgr = SessionSecurity(timeout_seconds=60)
        
        session_mgr.create_session('test_session', {})
        
        original_activity = session_mgr.sessions['test_session']['last_activity']
        time.sleep(0.1)
        
        assert session_mgr.update_activity('test_session'), "Should update existing session"
        new_activity = session_mgr.sessions['test_session']['last_activity']
        
        assert new_activity > original_activity, "Activity timestamp should be updated"
        assert not session_mgr.update_activity('nonexistent'), "Should not update nonexistent session"
    
    def test_session_expiration(self):
        """Test session expiration"""
        session_mgr = SessionSecurity(timeout_seconds=1)
        
        session_mgr.create_session('test_session', {})
        assert session_mgr.is_session_valid('test_session')
        
        time.sleep(1.1)
        assert not session_mgr.is_session_valid('test_session'), "Session should expire"
    
    def test_cleanup_expired_sessions(self):
        """Test cleanup of expired sessions"""
        session_mgr = SessionSecurity(timeout_seconds=1)
        
        session_mgr.create_session('session1', {})
        session_mgr.create_session('session2', {})
        
        assert len(session_mgr.sessions) == 2
        
        time.sleep(1.1)
        expired_count = session_mgr.cleanup_expired_sessions()
        
        assert expired_count == 2, "Should clean up 2 expired sessions"
        assert len(session_mgr.sessions) == 0
    
    def test_revoke_session(self):
        """Test session revocation"""
        session_mgr = SessionSecurity(timeout_seconds=60)
        
        session_mgr.create_session('test_session', {})
        assert session_mgr.is_session_valid('test_session')
        
        assert session_mgr.revoke_session('test_session'), "Should revoke existing session"
        assert not session_mgr.is_session_valid('test_session'), "Revoked session should be invalid"
        assert not session_mgr.revoke_session('nonexistent'), "Should not revoke nonexistent session"


class TestSecurityManager:
    """Test security manager functionality"""
    
    def test_security_manager_initialization(self):
        """Test security manager initialization"""
        config = SecurityConfig()
        manager = SecurityManager(config)
        
        assert manager.config == config
        assert manager.validator is not None
        assert manager.rate_limiter is not None
        assert manager.csrf_protection is not None
        assert manager.session_security is not None
    
    def test_validate_origin(self):
        """Test origin validation"""
        config = SecurityConfig()
        manager = SecurityManager(config)
        
        # Test allowed origins
        assert manager.validate_origin('chrome-extension://abcdef123456')
        assert manager.validate_origin('http://localhost:3000')
        
        # Test disallowed origins
        assert not manager.validate_origin('https://evil.com')
        assert not manager.validate_origin('')
        assert not manager.validate_origin(None)
    
    def test_validate_request_size(self):
        """Test request size validation"""
        config = SecurityConfig(max_request_size=100)
        manager = SecurityManager(config)
        
        small_data = {'key': 'value'}
        large_data = {'key': 'x' * 200}
        
        assert manager.validate_request_size(small_data), "Small request should be allowed"
        assert not manager.validate_request_size(large_data), "Large request should be rejected"
    
    def test_create_secure_response(self):
        """Test secure response creation"""
        config = SecurityConfig()
        manager = SecurityManager(config)
        
        response = manager.create_secure_response({'result': 'success'}, 'test_session')
        
        assert response['success'] is True
        assert response['data']['result'] == 'success'
        assert 'timestamp' in response
        assert 'csrf_token' in response
    
    def test_cleanup(self):
        """Test security manager cleanup"""
        config = SecurityConfig()
        manager = SecurityManager(config)
        
        # Create some sessions and tokens
        manager.session_security.create_session('session1', {})
        manager.csrf_protection.generate_token('session1')
        
        # Should not raise any exceptions
        manager.cleanup()
    
    def test_get_security_stats(self):
        """Test security statistics"""
        config = SecurityConfig()
        manager = SecurityManager(config)
        
        stats = manager.get_security_stats()
        
        assert 'active_sessions' in stats
        assert 'active_csrf_tokens' in stats
        assert 'rate_limit_enabled' in stats
        assert 'input_validation_enabled' in stats
        assert 'csrf_protection_enabled' in stats


class TestSecurityDecorators:
    """Test security decorators"""
    
    @pytest.mark.asyncio
    async def test_validate_input_decorator(self):
        """Test input validation decorator"""
        def validator(data):
            return data.get('valid', False), ['Invalid data'] if not data.get('valid') else []
        
        @validate_input(validator)
        async def test_function(data=None):
            return {'success': True}
        
        # Valid data
        result = await test_function(data={'valid': True})
        assert result['success'] is True
        
        # Invalid data
        result = await test_function(data={'valid': False})
        assert result['success'] is False
        assert 'Input validation failed' in result['error']
    
    @pytest.mark.asyncio
    async def test_rate_limit_decorator(self):
        """Test rate limit decorator"""
        limiter = RateLimiter(max_requests=2, window_seconds=60)
        
        @rate_limit(limiter)
        async def test_function(session_id='default'):
            return {'success': True}
        
        # First two calls should succeed
        result1 = await test_function()
        assert result1['success'] is True
        
        result2 = await test_function()
        assert result2['success'] is True
        
        # Third call should be rate limited
        result3 = await test_function()
        assert result3['success'] is False
        assert 'Rate limit exceeded' in result3['error']
    
    @pytest.mark.asyncio
    async def test_require_valid_session_decorator(self):
        """Test session validation decorator"""
        session_mgr = SessionSecurity(timeout_seconds=60)
        session_mgr.create_session('valid_session', {})
        
        @require_valid_session(session_mgr)
        async def test_function(session_id=None):
            return {'success': True}
        
        # Valid session
        result = await test_function(session_id='valid_session')
        assert result['success'] is True
        
        # Invalid session
        result = await test_function(session_id='invalid_session')
        assert result['success'] is False
        assert 'Invalid or expired session' in result['error']
        
        # No session
        result = await test_function()
        assert result['success'] is False
        assert 'Invalid or expired session' in result['error']


if __name__ == '__main__':
    pytest.main([__file__])