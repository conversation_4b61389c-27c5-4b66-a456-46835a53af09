/**
 * ScreenMonitorMCP Browser Extension - Type Definitions
 * 
 * Modern TypeScript type definitions for the refactored browser extension.
 * Provides type safety and better developer experience.
 */

// ============================================================================
// Core Extension Types
// ============================================================================

export interface ExtensionConfig {
  readonly mcpServerUrl: string;
  readonly allowedDomains: string[];
  readonly enabledFeatures: ExtensionFeature[];
  readonly extensionEnabled: boolean;
  readonly autoSyncDomains: boolean;
  readonly debugMode: boolean;
  readonly apiVersion: string;
}

export type ExtensionFeature = 
  | 'dom_monitoring'
  | 'smart_click'
  | 'text_extraction'
  | 'event_analysis'
  | 'form_automation'
  | 'navigation_tracking';

export interface SessionInfo {
  readonly sessionId: string;
  readonly domain: string;
  readonly url: string;
  readonly tabId: number;
  readonly features: ExtensionFeature[];
  readonly userAgent: string;
  readonly registeredAt: string;
  readonly lastActivity: string;
}

// ============================================================================
// Message Passing Types
// ============================================================================

export interface BaseMessage {
  readonly type: string;
  readonly timestamp: string;
  readonly sessionId?: string;
}

export interface MCPRegistrationMessage extends BaseMessage {
  readonly type: 'MCP_REGISTERED';
  readonly sessionId: string;
  readonly features: ExtensionFeature[];
  readonly success: boolean;
  readonly error?: string;
}

export interface DOMEventMessage extends BaseMessage {
  readonly type: 'DOM_EVENT';
  readonly data: DOMEventData;
}

export interface SmartClickMessage extends BaseMessage {
  readonly type: 'SMART_CLICK_REQUEST' | 'SMART_CLICK_EXECUTE';
  readonly data: SmartClickData;
}

export interface ConfigMessage extends BaseMessage {
  readonly type: 'GET_CONFIG' | 'SETTINGS_UPDATED';
  readonly config?: Partial<ExtensionConfig>;
}

export type ExtensionMessage = 
  | MCPRegistrationMessage
  | DOMEventMessage
  | SmartClickMessage
  | ConfigMessage;

// ============================================================================
// DOM Event Types
// ============================================================================

export interface DOMEventData {
  readonly eventType: DOMEventType;
  readonly url: string;
  readonly timestamp: string;
  readonly target?: ElementInfo;
  readonly mutations?: MutationInfo[];
  readonly coordinates?: Coordinates;
  readonly metadata?: Record<string, unknown>;
}

export type DOMEventType = 
  | 'page_load'
  | 'dom_change'
  | 'click'
  | 'form_submit'
  | 'navigation_change'
  | 'button_click'
  | 'input_change'
  | 'scroll'
  | 'resize';

export interface ElementInfo {
  readonly tagName: string;
  readonly id?: string;
  readonly className?: string;
  readonly textContent?: string;
  readonly selector: string;
  readonly attributes?: Record<string, string>;
  readonly boundingRect?: DOMRect;
}

export interface MutationInfo {
  readonly type: MutationRecordType;
  readonly target: ElementInfo;
  readonly addedNodes: number;
  readonly removedNodes: number;
  readonly attributeName?: string;
  readonly oldValue?: string;
}

export interface Coordinates {
  readonly x: number;
  readonly y: number;
}

// ============================================================================
// Smart Click Types
// ============================================================================

export interface SmartClickData {
  readonly description: string;
  readonly selector?: string;
  readonly confidence?: number;
  readonly dryRun?: boolean;
  readonly timeout?: number;
}

export interface SmartClickResult {
  readonly success: boolean;
  readonly elementFound: boolean;
  readonly element?: ElementInfo;
  readonly coordinates?: Coordinates;
  readonly confidence?: number;
  readonly error?: string;
  readonly executionTime?: number;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface APIResponse<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp: string;
  readonly requestId?: string;
}

export interface MCPServerResponse extends APIResponse {
  readonly sessionId?: string;
  readonly serverVersion?: string;
}

export interface HealthCheckResponse extends APIResponse {
  readonly status: 'healthy' | 'unhealthy';
  readonly server: string;
  readonly version: string;
  readonly webExtensionEnabled: boolean;
  readonly uptime?: number;
}

// ============================================================================
// Error Types
// ============================================================================

export interface ExtensionError {
  readonly code: ErrorCode;
  readonly message: string;
  readonly details?: Record<string, unknown>;
  readonly timestamp: string;
  readonly stack?: string;
}

export type ErrorCode = 
  | 'INVALID_CONFIG'
  | 'DOMAIN_NOT_ALLOWED'
  | 'SESSION_EXPIRED'
  | 'MCP_CONNECTION_FAILED'
  | 'ELEMENT_NOT_FOUND'
  | 'PERMISSION_DENIED'
  | 'NETWORK_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

// ============================================================================
// Storage Types
// ============================================================================

export interface StorageData {
  readonly config: ExtensionConfig;
  readonly sessions: Record<string, SessionInfo>;
  readonly cache: Record<string, unknown>;
  readonly lastSync: string;
}

// ============================================================================
// Event Handler Types
// ============================================================================

export type MessageHandler<T extends ExtensionMessage = ExtensionMessage> = (
  message: T,
  sender: chrome.runtime.MessageSender,
  sendResponse: (response?: unknown) => void
) => boolean | void | Promise<void>;

export type EventCallback<T = unknown> = (data: T) => void | Promise<void>;

// ============================================================================
// Utility Types
// ============================================================================

export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// ============================================================================
// Validation Types
// ============================================================================

export interface ValidationResult {
  readonly isValid: boolean;
  readonly errors: string[];
  readonly warnings: string[];
}

export interface Validator<T> {
  validate(data: unknown): ValidationResult & { data?: T };
}

// ============================================================================
// Logger Types
// ============================================================================

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  readonly level: LogLevel;
  readonly message: string;
  readonly timestamp: string;
  readonly context?: Record<string, unknown>;
  readonly error?: Error;
}

export interface Logger {
  debug(message: string, context?: Record<string, unknown>): void;
  info(message: string, context?: Record<string, unknown>): void;
  warn(message: string, context?: Record<string, unknown>): void;
  error(message: string, error?: Error, context?: Record<string, unknown>): void;
}