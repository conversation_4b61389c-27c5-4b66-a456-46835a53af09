/**
 * ScreenMonitorMCP Browser Extension - Background Service Worker
 * 
 * Modern service worker implementation with dependency injection,
 * proper error handling, and clean architecture patterns.
 */

import { initializeContainer, getContainer, ServiceKeys } from './core/Container';
import { createLogger } from './utils/Logger';
import { ConfigService } from './services/ConfigService';
import { MessageService } from './services/MessageService';
import { StorageService } from './services/StorageService';
import type { 
  ExtensionConfig, 
  SessionInfo, 
  MCPRegistrationMessage,
  DOMEventMessage,
  SmartClickMessage,
  ConfigMessage,
  APIResponse,
  Logger
} from './types';

// ============================================================================
// Background Service Worker Class
// ============================================================================

class BackgroundServiceWorker {
  private readonly logger: Logger;
  private readonly configService: ConfigService;
  private readonly messageService: MessageService;
  private readonly storageService: StorageService;
  private readonly activeSessions = new Map<string, SessionInfo>();
  private isInitialized = false;

  constructor() {
    // Initialize dependency injection container
    this.logger = createLogger({ 
      level: 'debug', 
      prefix: '[ScreenMonitorMCP-BG]' 
    });
    
    const container = initializeContainer(this.logger);
    
    // Register services
    container.registerInstance(ServiceKeys.LOGGER, this.logger);
    container.registerClass(ServiceKeys.CONFIG_SERVICE, ConfigService, {
      dependencies: [ServiceKeys.LOGGER]
    });
    container.registerClass(ServiceKeys.MESSAGE_SERVICE, MessageService, {
      dependencies: [ServiceKeys.LOGGER]
    });
    container.registerClass(ServiceKeys.STORAGE_SERVICE, StorageService, {
      dependencies: [ServiceKeys.LOGGER]
    });

    // Resolve services
    this.configService = container.resolve(ServiceKeys.CONFIG_SERVICE);
    this.messageService = container.resolve(ServiceKeys.MESSAGE_SERVICE);
    this.storageService = container.resolve(ServiceKeys.STORAGE_SERVICE);

    this.logger.info('Background service worker initialized');
  }

  /**
   * Initialize the service worker
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('Service worker already initialized');
      return;
    }

    try {
      this.logger.info('Initializing background service worker');

      // Initialize services
      await this.configService.initialize();
      
      // Setup message handlers
      this.setupMessageHandlers();
      
      // Setup extension event listeners
      this.setupExtensionEventListeners();

      // Load existing sessions
      await this.loadExistingSessions();

      this.isInitialized = true;
      this.logger.info('Background service worker initialization complete');
    } catch (error) {
      this.logger.error('Failed to initialize background service worker', error as Error);
      throw error;
    }
  }

  // ============================================================================
  // Message Handlers
  // ============================================================================

  private setupMessageHandlers(): void {
    // MCP Registration
    this.messageService.addHandler<MCPRegistrationMessage>(
      'MCP_REGISTER',
      this.handleMCPRegistration.bind(this)
    );

    // DOM Events
    this.messageService.addHandler<DOMEventMessage>(
      'DOM_EVENT',
      this.handleDOMEvent.bind(this)
    );

    // Smart Click
    this.messageService.addHandler<SmartClickMessage>(
      'SMART_CLICK_REQUEST',
      this.handleSmartClickRequest.bind(this)
    );

    // Configuration
    this.messageService.addHandler<ConfigMessage>(
      'GET_CONFIG',
      this.handleGetConfig.bind(this)
    );

    this.messageService.addHandler<ConfigMessage>(
      'SETTINGS_UPDATED',
      this.handleSettingsUpdate.bind(this)
    );

    this.logger.debug('Message handlers registered');
  }

  private async handleMCPRegistration(
    message: MCPRegistrationMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<APIResponse> {
    try {
      if (!sender.tab?.id || !sender.tab.url) {
        return this.messageService.createErrorResponse(
          'INVALID_CONFIG',
          'Invalid tab information'
        );
      }

      const domain = this.extractDomain(sender.tab.url);
      if (!domain) {
        return this.messageService.createErrorResponse(
          'INVALID_CONFIG',
          'Invalid URL format'
        );
      }

      // Check domain permission
      if (!this.configService.isDomainAllowed(domain)) {
        return this.messageService.createErrorResponse(
          'DOMAIN_NOT_ALLOWED',
          `Domain '${domain}' is not in whitelist`
        );
      }

      // Register with MCP server
      const mcpResult = await this.registerWithMCPServer({
        domain,
        features: message.features || [],
        userAgent: sender.tab.userAgent || '',
        sessionId: message.sessionId,
        tabId: sender.tab.id,
        url: sender.tab.url
      });

      if (mcpResult.success) {
        // Store session
        const sessionInfo: SessionInfo = {
          sessionId: message.sessionId,
          domain,
          url: sender.tab.url,
          tabId: sender.tab.id,
          features: message.features || [],
          userAgent: sender.tab.userAgent || '',
          registeredAt: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        };

        this.activeSessions.set(message.sessionId, sessionInfo);
        await this.saveSessionsToStorage();

        this.logger.info('MCP registration successful', { 
          sessionId: message.sessionId,
          domain,
          features: message.features
        });

        return this.messageService.createResponse(true, {
          sessionId: message.sessionId,
          features: message.features,
          serverVersion: '2.0.0'
        });
      } else {
        return this.messageService.createErrorResponse(
          'MCP_CONNECTION_FAILED',
          mcpResult.error || 'MCP server registration failed'
        );
      }
    } catch (error) {
      this.logger.error('MCP registration failed', error as Error);
      return this.messageService.createErrorResponse(
        'UNKNOWN_ERROR',
        'Registration failed'
      );
    }
  }

  private async handleDOMEvent(
    message: DOMEventMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<APIResponse> {
    try {
      const sessionId = message.sessionId;
      if (!sessionId || !this.activeSessions.has(sessionId)) {
        return this.messageService.createErrorResponse(
          'SESSION_EXPIRED',
          'Invalid or expired session'
        );
      }

      // Update session activity
      const session = this.activeSessions.get(sessionId)!;
      const updatedSession: SessionInfo = {
        ...session,
        lastActivity: new Date().toISOString()
      };
      this.activeSessions.set(sessionId, updatedSession);

      // Forward to MCP server
      await this.forwardDOMEventToMCP(sessionId, message.data);

      this.logger.debug('DOM event processed', { 
        sessionId,
        eventType: message.data.eventType
      });

      return this.messageService.createResponse(true);
    } catch (error) {
      this.logger.error('DOM event handling failed', error as Error);
      return this.messageService.createErrorResponse(
        'UNKNOWN_ERROR',
        'Event processing failed'
      );
    }
  }

  private async handleSmartClickRequest(
    message: SmartClickMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<APIResponse> {
    try {
      const sessionId = message.sessionId;
      if (!sessionId || !this.activeSessions.has(sessionId)) {
        return this.messageService.createErrorResponse(
          'SESSION_EXPIRED',
          'Invalid or expired session'
        );
      }

      // Forward to MCP server for processing
      const mcpResult = await this.forwardSmartClickToMCP(sessionId, message.data);

      if (mcpResult.success) {
        // Send execution command to content script
        if (sender.tab?.id) {
          await this.messageService.sendToContentScript(sender.tab.id, {
            type: 'SMART_CLICK_EXECUTE',
            data: mcpResult.data,
            timestamp: new Date().toISOString()
          });
        }

        return this.messageService.createResponse(true, mcpResult.data);
      } else {
        return this.messageService.createErrorResponse(
          'ELEMENT_NOT_FOUND',
          mcpResult.error || 'Smart click failed'
        );
      }
    } catch (error) {
      this.logger.error('Smart click request failed', error as Error);
      return this.messageService.createErrorResponse(
        'UNKNOWN_ERROR',
        'Smart click failed'
      );
    }
  }

  private async handleGetConfig(): Promise<APIResponse> {
    try {
      const config = this.configService.getConfig();
      const sessions = Array.from(this.activeSessions.values());

      return this.messageService.createResponse(true, {
        config,
        activeSessions: sessions,
        stats: this.getServiceStats()
      });
    } catch (error) {
      this.logger.error('Get config failed', error as Error);
      return this.messageService.createErrorResponse(
        'UNKNOWN_ERROR',
        'Failed to get configuration'
      );
    }
  }

  private async handleSettingsUpdate(
    message: ConfigMessage
  ): Promise<APIResponse> {
    try {
      if (!message.config) {
        return this.messageService.createErrorResponse(
          'VALIDATION_ERROR',
          'No configuration provided'
        );
      }

      const result = await this.configService.updateConfig(message.config);
      
      if (result.isValid) {
        // Re-validate existing sessions
        await this.revalidateSessions();
        
        return this.messageService.createResponse(true, {
          updated: true,
          config: this.configService.getConfig()
        });
      } else {
        return this.messageService.createErrorResponse(
          'VALIDATION_ERROR',
          result.errors.join(', ')
        );
      }
    } catch (error) {
      this.logger.error('Settings update failed', error as Error);
      return this.messageService.createErrorResponse(
        'UNKNOWN_ERROR',
        'Settings update failed'
      );
    }
  }

  // ============================================================================
  // Extension Event Listeners
  // ============================================================================

  private setupExtensionEventListeners(): void {
    // Tab activation
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        const tab = await chrome.tabs.get(activeInfo.tabId);
        await this.handleTabActivation(tab);
      } catch (error) {
        this.logger.error('Tab activation error', error as Error);
      }
    });

    // Tab updates
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        await this.handleTabUpdate(tab);
      }
    });

    // Tab removal
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.handleTabRemoval(tabId);
    });

    this.logger.debug('Extension event listeners registered');
  }

  private async handleTabActivation(tab: chrome.tabs.Tab): Promise<void> {
    if (!tab.url || !tab.id) return;

    const domain = this.extractDomain(tab.url);
    if (domain && this.configService.isDomainAllowed(domain)) {
      await this.initiateTabRegistration(tab);
    }
  }

  private async handleTabUpdate(tab: chrome.tabs.Tab): Promise<void> {
    if (!tab.url || !tab.id) return;

    const domain = this.extractDomain(tab.url);
    if (domain && this.configService.isDomainAllowed(domain)) {
      await this.initiateTabRegistration(tab);
    }
  }

  private handleTabRemoval(tabId: number): void {
    // Remove sessions for closed tab
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.tabId === tabId) {
        this.activeSessions.delete(sessionId);
        this.logger.debug('Session removed for closed tab', { sessionId, tabId });
      }
    }
  }

  // ============================================================================
  // MCP Server Communication
  // ============================================================================

  private async registerWithMCPServer(data: {
    domain: string;
    features: string[];
    userAgent: string;
    sessionId: string;
    tabId: number;
    url: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const config = this.configService.getConfig();
      const response = await fetch(`${config.mcpServerUrl}/api/extension/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        const result = await response.json();
        return { success: result.success, error: result.error };
      } else {
        return { success: false, error: `HTTP ${response.status}` };
      }
    } catch (error) {
      this.logger.error('MCP server registration failed', error as Error);
      return { success: false, error: (error as Error).message };
    }
  }

  private async forwardDOMEventToMCP(sessionId: string, eventData: any): Promise<void> {
    try {
      const config = this.configService.getConfig();
      await fetch(`${config.mcpServerUrl}/api/extension/dom-event`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          event_type: eventData.eventType,
          event_data: eventData,
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      this.logger.error('Failed to forward DOM event to MCP', error as Error);
    }
  }

  private async forwardSmartClickToMCP(
    sessionId: string, 
    clickData: any
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const config = this.configService.getConfig();
      const response = await fetch(`${config.mcpServerUrl}/api/extension/smart-click`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          element_description: clickData.description,
          css_selector: clickData.selector,
          confidence_threshold: clickData.confidence || 0.8
        })
      });

      if (response.ok) {
        const result = await response.json();
        return { success: result.success, data: result, error: result.error };
      } else {
        return { success: false, error: `HTTP ${response.status}` };
      }
    } catch (error) {
      this.logger.error('Failed to forward smart click to MCP', error as Error);
      return { success: false, error: (error as Error).message };
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  private async initiateTabRegistration(tab: chrome.tabs.Tab): Promise<void> {
    if (!tab.id) return;

    try {
      await this.messageService.sendToContentScript(tab.id, {
        type: 'INITIATE_REGISTRATION',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.debug('Failed to initiate tab registration', { tabId: tab.id });
    }
  }

  private extractDomain(url: string): string | null {
    try {
      const urlObj = new URL(url);
      
      if (urlObj.protocol === 'file:') {
        return 'file';
      }
      
      return urlObj.hostname;
    } catch {
      return null;
    }
  }

  private async loadExistingSessions(): Promise<void> {
    try {
      const sessions = await this.storageService.get<Record<string, SessionInfo>>('sessions', {});
      
      for (const [sessionId, session] of Object.entries(sessions)) {
        this.activeSessions.set(sessionId, session);
      }

      this.logger.debug('Existing sessions loaded', { count: this.activeSessions.size });
    } catch (error) {
      this.logger.error('Failed to load existing sessions', error as Error);
    }
  }

  private async saveSessionsToStorage(): Promise<void> {
    try {
      const sessions = Object.fromEntries(this.activeSessions.entries());
      await this.storageService.set('sessions', sessions);
    } catch (error) {
      this.logger.error('Failed to save sessions to storage', error as Error);
    }
  }

  private async revalidateSessions(): Promise<void> {
    const invalidSessions: string[] = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (!this.configService.isDomainAllowed(session.domain)) {
        invalidSessions.push(sessionId);
      }
    }

    for (const sessionId of invalidSessions) {
      this.activeSessions.delete(sessionId);
      this.logger.info('Session invalidated due to domain restriction', { sessionId });
    }

    if (invalidSessions.length > 0) {
      await this.saveSessionsToStorage();
    }
  }

  private getServiceStats(): Record<string, unknown> {
    return {
      activeSessions: this.activeSessions.size,
      messageService: this.messageService.getStats(),
      storageService: this.storageService.getCacheStats(),
      uptime: Date.now() - (globalThis as any).startTime
    };
  }
}

// ============================================================================
// Service Worker Initialization
// ============================================================================

// Track startup time
(globalThis as any).startTime = Date.now();

// Initialize service worker
const serviceWorker = new BackgroundServiceWorker();

// Handle service worker events
chrome.runtime.onStartup.addListener(async () => {
  await serviceWorker.initialize();
});

chrome.runtime.onInstalled.addListener(async () => {
  await serviceWorker.initialize();
});

// Initialize immediately if not already done
serviceWorker.initialize().catch(error => {
  console.error('Failed to initialize service worker:', error);
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BackgroundServiceWorker };
}