/**
 * ScreenMonitorMCP Browser Extension - Background Service Worker
 * 
 * Simplified working implementation for immediate loading
 */

console.log("ScreenMonitorMCP Extension: Background script loaded");

// Basic extension functionality
chrome.runtime.onInstalled.addListener(() => {
  console.log("ScreenMonitorMCP Extension installed");
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("Background received message:", message);
  
  // Simple response for now
  sendResponse({ success: true, data: "Background script is working" });
  return true;
});

// Handle tab updates
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && tab.url) {
    console.log("Tab updated:", tab.url);
  }
});
