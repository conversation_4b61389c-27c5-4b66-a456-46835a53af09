/**
 * ScreenMonitorMCP Browser Extension - Background Service Worker
 * 
 * Simplified working implementation for immediate loading
 */

console.log("ScreenMonitorMCP Extension: Background script loaded");

// Basic extension functionality
chrome.runtime.onInstalled.addListener(() => {
  console.log("ScreenMonitorMCP Extension installed");
});

// MCP Server configuration
const MCP_SERVER_URL = 'http://localhost:7777';

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("Background received message:", message);

  // Handle different message types
  switch (message.type) {
    case 'TEST_CONNECTION':
      testMCPConnection().then(result => {
        sendResponse(result);
      });
      return true; // Keep message channel open for async response

    case 'SMART_CLICK':
      handleSmartClick(message.data).then(result => {
        sendResponse(result);
      });
      return true;

    case 'GET_PAGE_INFO':
      getPageInfo(sender.tab).then(result => {
        sendResponse(result);
      });
      return true;

    default:
      sendResponse({ success: true, data: "Background script is working" });
  }

  return true;
});

// Test MCP server connection
async function testMCPConnection() {
  try {
    const response = await fetch(`${MCP_SERVER_URL}/api/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('MCP Server response:', data);
      return {
        success: true,
        data: 'MCP Server is running!',
        serverInfo: data
      };
    } else {
      return {
        success: false,
        error: `Server responded with status: ${response.status}`
      };
    }
  } catch (error) {
    console.error('MCP connection error:', error);
    return {
      success: false,
      error: 'Could not connect to MCP server. Make sure it is running on localhost:7777'
    };
  }
}

// Handle smart click requests
async function handleSmartClick(data) {
  try {
    const response = await fetch(`${MCP_SERVER_URL}/api/smart-click`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    console.error('Smart click error:', error);
    return { success: false, error: error.message };
  }
}

// Get page information
async function getPageInfo(tab) {
  try {
    const pageData = {
      url: tab.url,
      title: tab.title,
      timestamp: new Date().toISOString()
    };

    const response = await fetch(`${MCP_SERVER_URL}/api/page-info`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pageData)
    });

    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    console.error('Get page info error:', error);
    return { success: false, error: error.message };
  }
}

// Handle tab updates
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && tab.url) {
    console.log("Tab updated:", tab.url);
  }
});
