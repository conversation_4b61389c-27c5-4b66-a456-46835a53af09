/**
 * ScreenMonitorMCP Browser Extension - Logger Utility
 * 
 * Centralized logging system with different log levels and context support.
 * Provides structured logging for better debugging and monitoring.
 */

import type { Logger, LogLevel, LogEntry } from '../types';

// ============================================================================
// Logger Configuration
// ============================================================================

interface LoggerConfig {
  readonly level: LogLevel;
  readonly enableConsole: boolean;
  readonly enableStorage: boolean;
  readonly maxStoredLogs: number;
  readonly prefix: string;
}

const DEFAULT_CONFIG: LoggerConfig = {
  level: 'info',
  enableConsole: true,
  enableStorage: false,
  maxStoredLogs: 1000,
  prefix: '[ScreenMonitorMCP]',
};

// ============================================================================
// Logger Implementation
// ============================================================================

export class ExtensionLogger implements Logger {
  private readonly config: LoggerConfig;
  private readonly logs: LogEntry[] = [];
  private readonly logLevels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
  };

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  debug(message: string, context?: Record<string, unknown>): void {
    this.log('debug', message, context);
  }

  info(message: string, context?: Record<string, unknown>): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: Record<string, unknown>): void {
    this.log('warn', message, context);
  }

  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.log('error', message, context, error);
  }

  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): void {
    // Check if log level is enabled
    if (this.logLevels[level] < this.logLevels[this.config.level]) {
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      error,
    };

    // Store log entry
    if (this.config.enableStorage) {
      this.storeLogs(logEntry);
    }

    // Console output
    if (this.config.enableConsole) {
      this.outputToConsole(logEntry);
    }
  }

  private storeLogs(entry: LogEntry): void {
    this.logs.push(entry);

    // Maintain max logs limit
    if (this.logs.length > this.config.maxStoredLogs) {
      this.logs.shift();
    }
  }

  private outputToConsole(entry: LogEntry): void {
    const prefix = `${this.config.prefix} [${entry.level.toUpperCase()}]`;
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const message = `${prefix} ${timestamp} - ${entry.message}`;

    const consoleMethod = this.getConsoleMethod(entry.level);
    
    if (entry.context || entry.error) {
      const additionalData: Record<string, unknown> = {};
      
      if (entry.context) {
        additionalData.context = entry.context;
      }
      
      if (entry.error) {
        additionalData.error = {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack,
        };
      }
      
      consoleMethod(message, additionalData);
    } else {
      consoleMethod(message);
    }
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case 'debug':
        return console.debug.bind(console);
      case 'info':
        return console.info.bind(console);
      case 'warn':
        return console.warn.bind(console);
      case 'error':
        return console.error.bind(console);
      default:
        return console.log.bind(console);
    }
  }

  /**
   * Get stored log entries
   */
  getLogs(level?: LogLevel, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return [...filteredLogs];
  }

  /**
   * Clear stored logs
   */
  clearLogs(): void {
    this.logs.length = 0;
  }

  /**
   * Get logger statistics
   */
  getStats(): {
    totalLogs: number;
    logsByLevel: Record<LogLevel, number>;
    oldestLog?: string;
    newestLog?: string;
  } {
    const logsByLevel: Record<LogLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0,
    };

    this.logs.forEach(log => {
      logsByLevel[log.level]++;
    });

    return {
      totalLogs: this.logs.length,
      logsByLevel,
      oldestLog: this.logs[0]?.timestamp,
      newestLog: this.logs[this.logs.length - 1]?.timestamp,
    };
  }

  /**
   * Create a child logger with additional context
   */
  createChild(context: Record<string, unknown>): Logger {
    return new ChildLogger(this, context);
  }

  /**
   * Update logger configuration
   */
  updateConfig(newConfig: Partial<LoggerConfig>): void {
    Object.assign(this.config, newConfig);
  }
}

// ============================================================================
// Child Logger for Contextual Logging
// ============================================================================

class ChildLogger implements Logger {
  constructor(
    private readonly parent: ExtensionLogger,
    private readonly childContext: Record<string, unknown>
  ) {}

  debug(message: string, context?: Record<string, unknown>): void {
    this.parent.debug(message, this.mergeContext(context));
  }

  info(message: string, context?: Record<string, unknown>): void {
    this.parent.info(message, this.mergeContext(context));
  }

  warn(message: string, context?: Record<string, unknown>): void {
    this.parent.warn(message, this.mergeContext(context));
  }

  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.parent.error(message, error, this.mergeContext(context));
  }

  private mergeContext(context?: Record<string, unknown>): Record<string, unknown> {
    return { ...this.childContext, ...context };
  }
}

// ============================================================================
// Logger Factory
// ============================================================================

export function createLogger(config?: Partial<LoggerConfig>): Logger {
  return new ExtensionLogger(config);
}

export function createChildLogger(
  parent: Logger,
  context: Record<string, unknown>
): Logger {
  if (parent instanceof ExtensionLogger) {
    return parent.createChild(context);
  }
  
  // Fallback for non-ExtensionLogger instances
  return new ChildLogger(parent as ExtensionLogger, context);
}

// ============================================================================
// Global Logger Instance
// ============================================================================

let globalLogger: Logger | null = null;

export function getLogger(): Logger {
  if (!globalLogger) {
    globalLogger = createLogger();
  }
  return globalLogger;
}

export function setLogger(logger: Logger): void {
  globalLogger = logger;
}

// ============================================================================
// Utility Functions
// ============================================================================

export function formatError(error: Error): Record<string, unknown> {
  return {
    name: error.name,
    message: error.message,
    stack: error.stack,
  };
}

export function sanitizeContext(context: Record<string, unknown>): Record<string, unknown> {
  const sanitized: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(context)) {
    if (typeof value === 'function') {
      sanitized[key] = '[Function]';
    } else if (value instanceof Error) {
      sanitized[key] = formatError(value);
    } else if (typeof value === 'object' && value !== null) {
      try {
        // Attempt to serialize, fallback to string representation
        JSON.stringify(value);
        sanitized[key] = value;
      } catch {
        sanitized[key] = '[Object]';
      }
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}