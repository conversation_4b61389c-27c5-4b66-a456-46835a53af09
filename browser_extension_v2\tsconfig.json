{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["chrome", "node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "coverage", "tests"]}