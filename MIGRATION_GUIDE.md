# Migration Guide: Browser Extension v1 → v2

This guide helps you migrate from the old browser extension implementation to the new refactored version.

## 🔄 Overview

The browser extension has been completely refactored with modern architecture, improved security, and better maintainability. This migration guide covers the changes and how to update your usage.

## 📁 File Structure Changes

### Old Structure (v1)
```
browser_extension/
├── manifest.json
├── background.js
├── content-script.js
└── popup.html

web_extension/
├── __init__.py
└── extension_server.py
```

### New Structure (v2)
```
browser_extension_v2/
├── src/
│   ├── types/index.ts
│   ├── core/Container.ts
│   ├── services/
│   ├── utils/
│   ├── config/
│   ├── background.ts
│   └── content-script.ts
├── tests/
├── manifest.json
└── package.json

web_extension_v2/
├── __init__.py
├── models.py
├── security.py
├── config.py
└── tests/
```

## 🚀 Key Improvements

### Architecture
- **TypeScript**: Full type safety
- **Dependency Injection**: Clean, testable code
- **Modular Design**: Separated concerns
- **Modern Patterns**: Async/await, ES6+

### Security
- **Input Validation**: Comprehensive validation
- **XSS Protection**: Built-in XSS prevention
- **Rate Limiting**: Configurable rate limits
- **CSRF Protection**: Token-based protection
- **Secure Sessions**: Session management

### Testing
- **Unit Tests**: Comprehensive test coverage
- **Integration Tests**: End-to-end testing
- **Mocking**: Chrome API mocks
- **Coverage Reports**: Test coverage tracking

## 🔧 Migration Steps

### 1. Update Extension Files

#### Replace Old Extension
1. Remove the old `browser_extension/` folder
2. Use the new `browser_extension_v2/` folder
3. Build the new extension: `npm run build`
4. Load the new extension in Chrome

#### Update Manifest
The new manifest uses Manifest V3:

**Old (v1):**
```json
{
  "manifest_version": 2,
  "background": {
    "scripts": ["background.js"],
    "persistent": false
  }
}
```

**New (v2):**
```json
{
  "manifest_version": 3,
  "background": {
    "service_worker": "dist/background.js",
    "type": "module"
  }
}
```

### 2. Update Server Integration

#### Python Server Changes

**Old (v1):**
```python
from web_extension import is_extension_enabled, get_enabled_features

if is_extension_enabled():
    features = get_enabled_features()
```

**New (v2):**
```python
from web_extension_v2 import get_config, is_feature_enabled

config = get_config()
if config.extension.enabled:
    if is_feature_enabled('web_monitoring'):
        # Feature is enabled
```

#### API Endpoint Changes

**Old (v1):**
```python
# Simple function calls
register_browser_extension(domain, features)
web_dom_event(session_id, event_type, event_data)
```

**New (v2):**
```python
# Structured API with validation
from web_extension_v2.models import RegistrationRequest, DOMEventRequest
from web_extension_v2.security import SecurityManager

# Validate and process registration
request = RegistrationRequest(domain=domain, features=features)
security_manager.validate_request(request)
```

### 3. Configuration Migration

#### Environment Variables

**Old (v1):**
```bash
ENABLE_WEB_EXTENSION=true
ALLOWED_DOMAINS=localhost,github.com
```

**New (v2):**
```bash
WEB_EXTENSION_ENV=development
ENABLE_WEB_EXTENSION=true
ALLOWED_DOMAINS=localhost,127.0.0.1,github.com
ENABLE_RATE_LIMITING=true
LOG_LEVEL=DEBUG
```

#### Configuration Files

**Old (v1):** No configuration files

**New (v2):**
```json
{
  "environment": "development",
  "server": {
    "host": "localhost",
    "port": 7777
  },
  "security": {
    "enable_rate_limiting": true,
    "rate_limit_requests": 100
  },
  "extension": {
    "enabled": true,
    "allowed_domains": ["localhost", "127.0.0.1"]
  }
}
```

### 4. Code Updates

#### Message Passing

**Old (v1):**
```javascript
// Simple message passing
chrome.runtime.sendMessage({
  type: 'DOM_EVENT',
  data: eventData
});
```

**New (v2):**
```typescript
// Type-safe message passing
import { MessageService } from './services/MessageService';

const messageService = new MessageService(logger);
await messageService.sendToBackground({
  type: 'DOM_EVENT',
  data: eventData,
  timestamp: new Date().toISOString()
});
```

#### Configuration Access

**Old (v1):**
```javascript
// No centralized configuration
const serverUrl = 'http://localhost:7777';
```

**New (v2):**
```typescript
// Centralized configuration
import { getConfig } from './config';

const config = getConfig();
const serverUrl = config.mcpServerUrl;
```

#### Error Handling

**Old (v1):**
```javascript
// Basic error handling
try {
  // Some operation
} catch (error) {
  console.error(error);
}
```

**New (v2):**
```typescript
// Structured error handling
import { Logger } from './types';

try {
  // Some operation
} catch (error) {
  this.logger.error('Operation failed', error as Error, {
    context: 'additional context'
  });
  throw new ExtensionError('OPERATION_FAILED', 'Operation failed');
}
```

## 🔒 Security Migration

### Input Validation

**Old (v1):** No systematic validation

**New (v2):**
```typescript
import { validators } from './utils/Validator';

// Validate all inputs
const urlResult = validators.url.validate(userInput);
if (!urlResult.isValid) {
  throw new Error('Invalid URL');
}
```

### XSS Protection

**Old (v1):** No XSS protection

**New (v2):**
```typescript
import { sanitizeHTML } from './utils/Validator';

// Sanitize all user content
const safeContent = sanitizeHTML(userContent);
```

### Rate Limiting

**Old (v1):** No rate limiting

**New (v2):**
```python
from web_extension_v2.security import RateLimiter

limiter = RateLimiter(max_requests=100, window_seconds=60)
if not limiter.is_allowed(user_id):
    return {'error': 'Rate limit exceeded'}
```

## 🧪 Testing Migration

### Old Testing (v1)
- No systematic testing
- Manual testing only

### New Testing (v2)
```bash
# Install dependencies
npm install

# Run tests
npm test

# Run with coverage
npm run test:coverage

# Python tests
python -m pytest
```

### Test Examples

```typescript
// TypeScript tests
import { setupTestEnvironment, TestDataFactory } from '../setup';

describe('ConfigService', () => {
  let cleanup: () => void;

  beforeAll(() => {
    cleanup = setupTestEnvironment();
  });

  it('should update configuration', async () => {
    const config = TestDataFactory.createExtensionConfig();
    // Test implementation
  });
});
```

```python
# Python tests
import pytest
from web_extension_v2.security import InputValidator

def test_domain_validation():
    is_valid, error = InputValidator.validate_domain("localhost")
    assert is_valid
    assert error is None
```

## 📊 Performance Improvements

### Old Performance (v1)
- No caching
- Synchronous operations
- No optimization

### New Performance (v2)
- **Caching**: Storage service with caching
- **Async Operations**: Modern async/await patterns
- **Rate Limiting**: Prevents abuse
- **Optimized DOM Monitoring**: Throttled events
- **Memory Management**: Automatic cleanup

## 🔧 Development Workflow

### Old Workflow (v1)
1. Edit JavaScript files
2. Reload extension manually
3. Test manually

### New Workflow (v2)
1. Edit TypeScript files
2. Automatic compilation with `npm run watch`
3. Automated testing with `npm test`
4. Code quality checks with `npm run lint`
5. Type checking with `npm run type-check`

## 🚨 Breaking Changes

### API Changes
- All APIs now require proper validation
- Session management is mandatory
- Rate limiting is enforced
- CSRF tokens required for state-changing operations

### Configuration Changes
- Environment-based configuration required
- Domain whitelist is strictly enforced
- Feature flags control functionality

### Message Format Changes
- All messages must include timestamps
- Type field is required
- Validation is enforced

## 🔄 Rollback Plan

If you need to rollback to v1:

1. **Keep v1 files**: Don't delete the old `browser_extension/` and `web_extension/` folders until migration is complete
2. **Environment variables**: Keep old environment variables as backup
3. **Configuration**: Document current v1 configuration
4. **Testing**: Test v2 thoroughly before removing v1

### Rollback Steps
1. Disable v2 extension in Chrome
2. Re-enable v1 extension
3. Revert environment variables
4. Restart MCP server
5. Test functionality

## ✅ Migration Checklist

### Pre-Migration
- [ ] Backup current configuration
- [ ] Document current functionality
- [ ] Test current system
- [ ] Review new features and requirements

### Migration
- [ ] Install Node.js and npm
- [ ] Build new extension: `npm run build`
- [ ] Update environment variables
- [ ] Configure domain whitelist
- [ ] Load new extension in browser
- [ ] Update server integration code

### Post-Migration
- [ ] Test all functionality
- [ ] Verify security features
- [ ] Run automated tests
- [ ] Monitor performance
- [ ] Update documentation
- [ ] Train team on new features

### Validation
- [ ] Extension loads correctly
- [ ] MCP server communication works
- [ ] Domain whitelist is enforced
- [ ] Smart click functionality works
- [ ] DOM monitoring is active
- [ ] Error handling is working
- [ ] Logging is configured
- [ ] Tests pass

## 🆘 Troubleshooting

### Common Migration Issues

#### Extension Won't Load
- Check Manifest V3 compatibility
- Verify build process completed
- Check browser console for errors

#### Server Connection Failed
- Verify new API endpoints
- Check CORS configuration
- Validate domain whitelist

#### Configuration Issues
- Check environment variables
- Verify configuration file format
- Review security settings

#### Performance Issues
- Check rate limiting settings
- Verify caching configuration
- Monitor memory usage

### Getting Help

1. Check the troubleshooting sections in the READMEs
2. Review test files for usage examples
3. Check browser and server logs
4. Create an issue with detailed information

## 📚 Additional Resources

- [Browser Extension v2 README](browser_extension_v2/README.md)
- [Web Extension v2 README](web_extension_v2/README.md)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Chrome Extension Manifest V3](https://developer.chrome.com/docs/extensions/mv3/)
- [Testing Best Practices](https://jestjs.io/docs/getting-started)

## 🎯 Next Steps

After successful migration:

1. **Explore New Features**: Try the new security features and configuration options
2. **Customize Configuration**: Adjust settings for your specific needs
3. **Add Tests**: Write tests for your specific use cases
4. **Monitor Performance**: Use the new monitoring capabilities
5. **Contribute**: Consider contributing improvements back to the project

The migration to v2 provides a solid foundation for future enhancements and better security. Take time to explore the new capabilities and provide feedback for further improvements.