/**
 * ScreenMonitorMCP Browser Extension - Content Script
 * 
 * Modern content script with clean architecture, proper error handling,
 * and type-safe DOM monitoring and interaction capabilities.
 */

import { createLogger } from './utils/Logger';
import type { 
  ExtensionMessage,
  DOMEventData,
  DOMEventType,
  ElementInfo,
  SmartClickData,
  SmartClickResult,
  Coordinates,
  Logger,
  ExtensionConfig,
  SessionInfo
} from './types';

// ============================================================================
// Content Script Configuration
// ============================================================================

interface ContentScriptConfig {
  readonly sessionId: string;
  readonly enabledFeatures: string[];
  readonly debugMode: boolean;
  readonly eventThrottleMs: number;
  readonly maxEventQueueSize: number;
}

// ============================================================================
// Content Script Class
// ============================================================================

class ScreenMonitorContentScript {
  private readonly logger: Logger;
  private config: ContentScriptConfig | null = null;
  private sessionInfo: SessionInfo | null = null;
  private isConnected = false;
  private observer: MutationObserver | null = null;
  private eventQueue: DOMEventData[] = [];
  private isProcessingEvents = false;
  private lastEventTime = 0;

  constructor() {
    this.logger = createLogger({ 
      level: 'debug', 
      prefix: '[ScreenMonitorMCP-CS]' 
    });
    
    this.logger.info('Content script initialized', { 
      url: window.location.href,
      domain: window.location.hostname
    });

    this.initialize();
  }

  /**
   * Initialize content script
   */
  private async initialize(): Promise<void> {
    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.startMonitoring());
      } else {
        this.startMonitoring();
      }

      // Setup message listener
      this.setupMessageListener();

      // Setup page communication
      this.setupPageCommunication();

      // Request configuration from background
      await this.requestConfiguration();

      this.logger.debug('Content script initialization complete');
    } catch (error) {
      this.logger.error('Content script initialization failed', error as Error);
    }
  }

  /**
   * Start monitoring DOM and page events
   */
  private async startMonitoring(): Promise<void> {
    if (!this.isConnected || this.observer) {
      return;
    }

    try {
      this.logger.info('Starting DOM monitoring');

      // Setup mutation observer
      this.setupMutationObserver();

      // Setup event listeners
      this.setupEventListeners();

      // Analyze initial page state
      await this.analyzeInitialPage();

      // Notify test pages
      this.notifyTestPages();

      this.logger.info('DOM monitoring started successfully');
    } catch (error) {
      this.logger.error('Failed to start monitoring', error as Error);
    }
  }

  /**
   * Stop monitoring
   */
  private stopMonitoring(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    this.logger.info('DOM monitoring stopped');
  }

  // ============================================================================
  // Message Handling
  // ============================================================================

  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });
  }

  private async handleMessage(
    message: ExtensionMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      this.logger.debug('Message received', { type: message.type });

      switch (message.type) {
        case 'MCP_REGISTERED':
          await this.handleMCPRegistered(message, sendResponse);
          break;

        case 'SMART_CLICK_EXECUTE':
          await this.handleSmartClickExecute(message, sendResponse);
          break;

        case 'INITIATE_REGISTRATION':
          await this.handleInitiateRegistration(sendResponse);
          break;

        case 'GET_PAGE_INFO':
          await this.handleGetPageInfo(sendResponse);
          break;

        default:
          this.logger.warn('Unknown message type', { type: message.type });
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      this.logger.error('Message handling error', error as Error);
      sendResponse({ success: false, error: (error as Error).message });
    }
  }

  private async handleMCPRegistered(
    message: any,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    this.sessionInfo = {
      sessionId: message.sessionId,
      domain: window.location.hostname,
      url: window.location.href,
      tabId: 0, // Will be set by background
      features: message.features || [],
      userAgent: navigator.userAgent,
      registeredAt: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };

    this.isConnected = message.success;

    if (this.isConnected) {
      await this.startMonitoring();
      this.logger.info('MCP registration confirmed', { 
        sessionId: this.sessionInfo.sessionId 
      });
    } else {
      this.logger.warn('MCP registration failed', { error: message.error });
    }

    sendResponse({ received: true, connected: this.isConnected });
  }

  private async handleSmartClickExecute(
    message: any,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    const result = await this.executeSmartClick(message.data);
    sendResponse(result);
  }

  private async handleInitiateRegistration(
    sendResponse: (response?: any) => void
  ): Promise<void> {
    await this.registerWithBackground();
    sendResponse({ initiated: true });
  }

  private async handleGetPageInfo(
    sendResponse: (response?: any) => void
  ): Promise<void> {
    const pageInfo = this.getPageInfo();
    sendResponse({ success: true, data: pageInfo });
  }

  // ============================================================================
  // DOM Monitoring
  // ============================================================================

  private setupMutationObserver(): void {
    this.observer = new MutationObserver((mutations) => {
      this.handleMutations(mutations);
    });

    const observerConfig: MutationObserverInit = {
      childList: true,
      subtree: true,
      attributes: true,
      attributeOldValue: true,
      characterData: true,
      characterDataOldValue: true
    };

    this.observer.observe(document.body, observerConfig);
    this.logger.debug('Mutation observer setup complete');
  }

  private handleMutations(mutations: MutationRecord[]): void {
    const significantMutations = mutations.filter(mutation => 
      this.isSignificantMutation(mutation)
    );

    if (significantMutations.length > 0) {
      const eventData: DOMEventData = {
        eventType: 'dom_change',
        url: window.location.href,
        timestamp: new Date().toISOString(),
        mutations: significantMutations.map(mutation => ({
          type: mutation.type,
          target: this.getElementInfo(mutation.target as Element),
          addedNodes: mutation.addedNodes.length,
          removedNodes: mutation.removedNodes.length,
          attributeName: mutation.attributeName,
          oldValue: mutation.oldValue
        }))
      };

      this.queueEvent(eventData);
    }
  }

  private isSignificantMutation(mutation: MutationRecord): boolean {
    // Filter out insignificant changes
    if (mutation.type === 'attributes') {
      const ignoredAttributes = [
        'style', 'class', 'data-focus', 'aria-selected', 
        'data-hover', 'data-active', 'tabindex'
      ];
      return !ignoredAttributes.includes(mutation.attributeName || '');
    }

    if (mutation.type === 'childList') {
      // Ignore text-only changes and script insertions
      const hasSignificantNodes = Array.from(mutation.addedNodes).some(node => 
        node.nodeType === Node.ELEMENT_NODE && 
        (node as Element).tagName !== 'SCRIPT'
      );
      return hasSignificantNodes || mutation.removedNodes.length > 0;
    }

    return true;
  }

  private setupEventListeners(): void {
    // Click events
    document.addEventListener('click', (event) => {
      this.handleClickEvent(event);
    }, { passive: true });

    // Form submissions
    document.addEventListener('submit', (event) => {
      this.handleFormSubmit(event);
    }, { passive: true });

    // Input changes
    document.addEventListener('input', (event) => {
      this.handleInputChange(event);
    }, { passive: true });

    // Navigation changes (for SPAs)
    this.setupNavigationListener();

    this.logger.debug('Event listeners setup complete');
  }

  private handleClickEvent(event: MouseEvent): void {
    const eventData: DOMEventData = {
      eventType: 'click',
      url: window.location.href,
      timestamp: new Date().toISOString(),
      target: this.getElementInfo(event.target as Element),
      coordinates: { x: event.clientX, y: event.clientY }
    };

    this.queueEvent(eventData);
  }

  private handleFormSubmit(event: SubmitEvent): void {
    const eventData: DOMEventData = {
      eventType: 'form_submit',
      url: window.location.href,
      timestamp: new Date().toISOString(),
      target: this.getElementInfo(event.target as Element)
    };

    this.queueEvent(eventData);
  }

  private handleInputChange(event: Event): void {
    // Throttle input events
    const now = Date.now();
    if (now - this.lastEventTime < 500) {
      return;
    }
    this.lastEventTime = now;

    const eventData: DOMEventData = {
      eventType: 'input_change',
      url: window.location.href,
      timestamp: new Date().toISOString(),
      target: this.getElementInfo(event.target as Element)
    };

    this.queueEvent(eventData);
  }

  private setupNavigationListener(): void {
    let lastUrl = window.location.href;
    
    const checkUrlChange = () => {
      const currentUrl = window.location.href;
      if (currentUrl !== lastUrl) {
        const eventData: DOMEventData = {
          eventType: 'navigation_change',
          url: currentUrl,
          timestamp: new Date().toISOString(),
          metadata: { previousUrl: lastUrl }
        };

        this.queueEvent(eventData);
        lastUrl = currentUrl;
      }
    };

    // Check for URL changes periodically (for SPAs)
    setInterval(checkUrlChange, 1000);

    // Also listen for popstate events
    window.addEventListener('popstate', checkUrlChange);
  }

  // ============================================================================
  // Smart Click Implementation
  // ============================================================================

  private async executeSmartClick(clickData: SmartClickData): Promise<SmartClickResult> {
    try {
      this.logger.debug('Executing smart click', { description: clickData.description });

      let element: Element | null = null;

      // Try CSS selector first
      if (clickData.selector) {
        element = document.querySelector(clickData.selector);
      }

      // Fallback to text-based search
      if (!element) {
        element = this.findElementByDescription(clickData.description);
      }

      if (element) {
        // Scroll into view
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center',
          inline: 'center'
        });

        // Highlight element
        this.highlightElement(element);

        // Execute click after delay
        await this.delay(500);
        
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        
        element.dispatchEvent(clickEvent);

        const result: SmartClickResult = {
          success: true,
          elementFound: true,
          element: this.getElementInfo(element),
          coordinates: this.getElementCenter(element),
          confidence: 0.9,
          executionTime: Date.now()
        };

        this.logger.info('Smart click executed successfully', { 
          description: clickData.description,
          element: result.element?.tagName
        });

        return result;
      } else {
        this.logger.warn('Element not found for smart click', { 
          description: clickData.description 
        });

        return {
          success: false,
          elementFound: false,
          error: 'Element not found',
          executionTime: Date.now()
        };
      }
    } catch (error) {
      this.logger.error('Smart click execution failed', error as Error);
      return {
        success: false,
        elementFound: false,
        error: (error as Error).message,
        executionTime: Date.now()
      };
    }
  }

  private findElementByDescription(description: string): Element | null {
    const searchText = description.toLowerCase();

    // Search buttons
    const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]'));
    for (const button of buttons) {
      const text = button.textContent?.toLowerCase() || '';
      const value = (button as HTMLInputElement).value?.toLowerCase() || '';
      if (text.includes(searchText) || value.includes(searchText)) {
        return button;
      }
    }

    // Search links
    const links = Array.from(document.querySelectorAll('a'));
    for (const link of links) {
      if (link.textContent?.toLowerCase().includes(searchText)) {
        return link;
      }
    }

    // Search by aria-label
    const ariaElements = Array.from(document.querySelectorAll('[aria-label]'));
    for (const element of ariaElements) {
      const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
      if (ariaLabel.includes(searchText)) {
        return element;
      }
    }

    // Search by title attribute
    const titleElements = Array.from(document.querySelectorAll('[title]'));
    for (const element of titleElements) {
      const title = element.getAttribute('title')?.toLowerCase() || '';
      if (title.includes(searchText)) {
        return element;
      }
    }

    return null;
  }

  private highlightElement(element: Element): void {
    const originalStyle = (element as HTMLElement).style.cssText;
    
    (element as HTMLElement).style.cssText += `
      border: 3px solid #ff6b6b !important; 
      background-color: rgba(255, 107, 107, 0.1) !important;
      box-shadow: 0 0 10px rgba(255, 107, 107, 0.5) !important;
    `;

    setTimeout(() => {
      (element as HTMLElement).style.cssText = originalStyle;
    }, 2000);
  }

  private getElementCenter(element: Element): Coordinates {
    const rect = element.getBoundingClientRect();
    return {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    };
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  private getElementInfo(element: Element | Node): ElementInfo {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
      return { 
        tagName: 'TEXT_NODE', 
        selector: 'text' 
      };
    }

    const el = element as Element;
    const rect = el.getBoundingClientRect();

    return {
      tagName: el.tagName,
      id: el.id || undefined,
      className: el.className || undefined,
      textContent: el.textContent?.substring(0, 100) || undefined,
      selector: this.generateSelector(el),
      attributes: this.getElementAttributes(el),
      boundingRect: {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        top: rect.top,
        right: rect.right,
        bottom: rect.bottom,
        left: rect.left,
        toJSON: () => ({})
      } as DOMRect
    };
  }

  private generateSelector(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes[0]}`;
      }
    }

    return element.tagName.toLowerCase();
  }

  private getElementAttributes(element: Element): Record<string, string> {
    const attributes: Record<string, string> = {};
    
    for (const attr of element.attributes) {
      if (attr.name !== 'style' && attr.name !== 'class') {
        attributes[attr.name] = attr.value;
      }
    }

    return attributes;
  }

  private queueEvent(eventData: DOMEventData): void {
    // Add to queue
    this.eventQueue.push(eventData);

    // Limit queue size
    if (this.eventQueue.length > 100) {
      this.eventQueue.shift();
    }

    // Process queue
    if (!this.isProcessingEvents) {
      this.processEventQueue();
    }
  }

  private async processEventQueue(): Promise<void> {
    if (this.isProcessingEvents || this.eventQueue.length === 0 || !this.isConnected) {
      return;
    }

    this.isProcessingEvents = true;

    try {
      while (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift()!;

        await chrome.runtime.sendMessage({
          type: 'DOM_EVENT',
          sessionId: this.sessionInfo?.sessionId,
          data: event,
          timestamp: new Date().toISOString()
        });

        // Rate limiting
        await this.delay(100);
      }
    } catch (error) {
      this.logger.error('Event queue processing failed', error as Error);
    } finally {
      this.isProcessingEvents = false;
    }
  }

  private async analyzeInitialPage(): Promise<void> {
    const pageInfo = this.getPageInfo();
    
    const eventData: DOMEventData = {
      eventType: 'page_load',
      url: window.location.href,
      timestamp: new Date().toISOString(),
      metadata: pageInfo
    };

    this.queueEvent(eventData);
  }

  private getPageInfo(): Record<string, unknown> {
    return {
      title: document.title,
      url: window.location.href,
      domain: window.location.hostname,
      elementCounts: {
        buttons: document.querySelectorAll('button').length,
        links: document.querySelectorAll('a').length,
        forms: document.querySelectorAll('form').length,
        inputs: document.querySelectorAll('input').length,
        images: document.querySelectorAll('img').length
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      readyState: document.readyState
    };
  }

  private async registerWithBackground(): Promise<void> {
    try {
      const sessionId = this.generateSessionId();
      const features = ['dom_monitoring', 'smart_click', 'text_extraction'];

      await chrome.runtime.sendMessage({
        type: 'MCP_REGISTER',
        sessionId,
        features,
        timestamp: new Date().toISOString()
      });

      this.logger.debug('Registration request sent', { sessionId });
    } catch (error) {
      this.logger.error('Failed to register with background', error as Error);
    }
  }

  private setupPageCommunication(): void {
    // Listen for messages from test pages
    window.addEventListener('message', (event) => {
      if (event.data.type === 'REQUEST_EXTENSION_STATUS' && event.data.source === 'test-page') {
        this.sendStatusToTestPage();
      }
    });
  }

  private sendStatusToTestPage(): void {
    window.postMessage({
      type: 'EXTENSION_STATUS_RESPONSE',
      connected: this.isConnected,
      sessionId: this.sessionInfo?.sessionId,
      features: this.sessionInfo?.features || [],
      url: window.location.href,
      timestamp: new Date().toISOString()
    }, '*');
  }

  private notifyTestPages(): void {
    if ((window as any).screenMonitorTestPage) {
      window.postMessage({
        type: 'SCREEN_MONITOR_EXTENSION',
        status: 'connected',
        sessionId: this.sessionInfo?.sessionId,
        message: 'Extension is active and monitoring this page'
      }, '*');
    }
  }

  private generateSessionId(): string {
    return `cs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async requestConfiguration(): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_CONFIG',
        timestamp: new Date().toISOString()
      });

      if (response.success && response.data) {
        this.config = {
          sessionId: '',
          enabledFeatures: response.data.config?.enabledFeatures || [],
          debugMode: response.data.config?.debugMode || false,
          eventThrottleMs: 100,
          maxEventQueueSize: 100
        };

        this.logger.debug('Configuration received', { config: this.config });
      }
    } catch (error) {
      this.logger.error('Failed to request configuration', error as Error);
    }
  }
}

// ============================================================================
// Content Script Initialization
// ============================================================================

// Initialize content script when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ScreenMonitorContentScript();
  });
} else {
  new ScreenMonitorContentScript();
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ScreenMonitorContentScript };
}