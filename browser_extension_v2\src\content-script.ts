/**
 * ScreenMonitorMCP Browser Extension - Content Script
 * 
 * Simplified working implementation for immediate loading
 */

console.log("ScreenMonitorMCP Extension: Content script loaded");

// Send a message to background script to confirm loading
chrome.runtime.sendMessage({ 
  type: "CONTENT_SCRIPT_LOADED", 
  url: window.location.href 
}, (response) => {
  console.log("Background response:", response);
});

// Enhanced DOM monitoring with MCP integration
document.addEventListener("click", (event) => {
  const target = event.target as HTMLElement;
  console.log("Click detected on:", target.tagName, target.id, target.className);

  // Send click data to background script for MCP processing
  const clickData = {
    tagName: target.tagName,
    id: target.id || null,
    className: target.className || null,
    textContent: target.textContent?.substring(0, 100) || null,
    coordinates: {
      x: event.clientX,
      y: event.clientY
    },
    timestamp: new Date().toISOString(),
    url: window.location.href
  };

  chrome.runtime.sendMessage({
    type: "DOM_EVENT",
    data: clickData
  });
});

// Smart click functionality
function performSmartClick(selector: string) {
  const element = document.querySelector(selector);
  if (element) {
    (element as HTMLElement).click();
    console.log("Smart click performed on:", selector);
    return { success: true, element: selector };
  } else {
    console.log("Element not found for smart click:", selector);
    return { success: false, error: "Element not found" };
  }
}

// Listen for smart click commands from background
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === "SMART_CLICK_EXECUTE") {
    const result = performSmartClick(message.selector);
    sendResponse(result);
  }
  return true;
});

// Page monitoring - send page info when loaded
window.addEventListener("load", () => {
  const pageInfo = {
    url: window.location.href,
    title: document.title,
    timestamp: new Date().toISOString(),
    elementCount: document.querySelectorAll("*").length,
    hasForm: document.querySelectorAll("form").length > 0,
    hasButtons: document.querySelectorAll("button, input[type='button'], input[type='submit']").length > 0
  };

  chrome.runtime.sendMessage({
    type: "GET_PAGE_INFO",
    data: pageInfo
  });
});
