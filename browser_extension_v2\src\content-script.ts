/**
 * ScreenMonitorMCP Browser Extension - Content Script
 * 
 * Simplified working implementation for immediate loading
 */

console.log("ScreenMonitorMCP Extension: Content script loaded");

// Send a message to background script to confirm loading
chrome.runtime.sendMessage({ 
  type: "CONTENT_SCRIPT_LOADED", 
  url: window.location.href 
}, (response) => {
  console.log("Background response:", response);
});

// Basic DOM monitoring
document.addEventListener("click", (event) => {
  const target = event.target as HTMLElement;
  console.log("Click detected on:", target.tagName, target.id, target.className);
});
