/**
 * ScreenMonitorMCP Browser Extension - Validation Utilities
 * 
 * Comprehensive validation utilities for input sanitization,
 * XSS protection, and data validation.
 */

import type { ValidationResult, Validator } from '../types';

// ============================================================================
// Validation Constants
// ============================================================================

const URL_REGEX = /^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$/;
const DOMAIN_REGEX = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
const SESSION_ID_REGEX = /^[a-zA-Z0-9_-]{10,50}$/;
const CSS_SELECTOR_REGEX = /^[a-zA-Z0-9\s\.\#\[\]\:\-_,>+~='"()]+$/;

// XSS patterns to detect and block
const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
  /<embed\b[^<]*>/gi,
  /<link\b[^<]*>/gi,
  /<meta\b[^<]*>/gi,
  /data:text\/html/gi,
  /vbscript:/gi,
];

// ============================================================================
// Base Validator Class
// ============================================================================

export class BaseValidator<T> implements Validator<T> {
  protected errors: string[] = [];
  protected warnings: string[] = [];

  validate(data: unknown): ValidationResult & { data?: T } {
    this.errors = [];
    this.warnings = [];

    try {
      const validatedData = this.performValidation(data);
      return {
        isValid: this.errors.length === 0,
        errors: [...this.errors],
        warnings: [...this.warnings],
        data: validatedData,
      };
    } catch (error) {
      this.addError(`Validation failed: ${(error as Error).message}`);
      return {
        isValid: false,
        errors: [...this.errors],
        warnings: [...this.warnings],
      };
    }
  }

  protected performValidation(data: unknown): T {
    throw new Error('performValidation must be implemented by subclass');
  }

  protected addError(message: string): void {
    this.errors.push(message);
  }

  protected addWarning(message: string): void {
    this.warnings.push(message);
  }

  protected isString(value: unknown): value is string {
    return typeof value === 'string';
  }

  protected isNumber(value: unknown): value is number {
    return typeof value === 'number' && !isNaN(value);
  }

  protected isBoolean(value: unknown): value is boolean {
    return typeof value === 'boolean';
  }

  protected isArray(value: unknown): value is unknown[] {
    return Array.isArray(value);
  }

  protected isObject(value: unknown): value is Record<string, unknown> {
    return typeof value === 'object' && value !== null && !Array.isArray(value);
  }
}

// ============================================================================
// Specific Validators
// ============================================================================

export class URLValidator extends BaseValidator<string> {
  protected performValidation(data: unknown): string {
    if (!this.isString(data)) {
      this.addError('URL must be a string');
      throw new Error('Invalid URL type');
    }

    const url = data.trim();
    
    if (!url) {
      this.addError('URL cannot be empty');
      throw new Error('Empty URL');
    }

    // Special handling for file:// protocol
    if (url.startsWith('file://')) {
      return url;
    }

    // Validate HTTP/HTTPS URLs
    if (!URL_REGEX.test(url)) {
      this.addError('Invalid URL format');
      throw new Error('Invalid URL format');
    }

    // Check for suspicious patterns
    if (this.containsXSS(url)) {
      this.addError('URL contains potentially malicious content');
      throw new Error('Malicious URL');
    }

    return url;
  }

  private containsXSS(url: string): boolean {
    return XSS_PATTERNS.some(pattern => pattern.test(url));
  }
}

export class DomainValidator extends BaseValidator<string> {
  protected performValidation(data: unknown): string {
    if (!this.isString(data)) {
      this.addError('Domain must be a string');
      throw new Error('Invalid domain type');
    }

    let domain = data.trim().toLowerCase();
    
    if (!domain) {
      this.addError('Domain cannot be empty');
      throw new Error('Empty domain');
    }

    // Remove protocol if present
    domain = domain.replace(/^https?:\/\//, '');
    
    // Remove path if present
    domain = domain.split('/')[0];
    
    // Special cases
    if (domain === 'localhost' || domain === '127.0.0.1' || domain === 'file') {
      return domain;
    }

    // Validate domain format
    if (!DOMAIN_REGEX.test(domain)) {
      this.addError('Invalid domain format');
      throw new Error('Invalid domain format');
    }

    // Check length
    if (domain.length > 253) {
      this.addError('Domain name too long');
      throw new Error('Domain too long');
    }

    return domain;
  }
}

export class SessionIdValidator extends BaseValidator<string> {
  protected performValidation(data: unknown): string {
    if (!this.isString(data)) {
      this.addError('Session ID must be a string');
      throw new Error('Invalid session ID type');
    }

    const sessionId = data.trim();
    
    if (!sessionId) {
      this.addError('Session ID cannot be empty');
      throw new Error('Empty session ID');
    }

    if (!SESSION_ID_REGEX.test(sessionId)) {
      this.addError('Invalid session ID format');
      throw new Error('Invalid session ID format');
    }

    return sessionId;
  }
}

export class CSSelectorValidator extends BaseValidator<string> {
  protected performValidation(data: unknown): string {
    if (!this.isString(data)) {
      this.addError('CSS selector must be a string');
      throw new Error('Invalid CSS selector type');
    }

    const selector = data.trim();
    
    if (!selector) {
      this.addError('CSS selector cannot be empty');
      throw new Error('Empty CSS selector');
    }

    // Basic CSS selector validation
    if (!CSS_SELECTOR_REGEX.test(selector)) {
      this.addError('Invalid CSS selector format');
      throw new Error('Invalid CSS selector format');
    }

    // Check for potentially dangerous selectors
    if (this.containsXSS(selector)) {
      this.addError('CSS selector contains potentially malicious content');
      throw new Error('Malicious CSS selector');
    }

    return selector;
  }

  private containsXSS(selector: string): boolean {
    return XSS_PATTERNS.some(pattern => pattern.test(selector));
  }
}

export class MessageValidator extends BaseValidator<Record<string, unknown>> {
  protected performValidation(data: unknown): Record<string, unknown> {
    if (!this.isObject(data)) {
      this.addError('Message must be an object');
      throw new Error('Invalid message type');
    }

    const message = data as Record<string, unknown>;

    // Validate required fields
    if (!message.type || !this.isString(message.type)) {
      this.addError('Message type is required and must be a string');
    }

    if (!message.timestamp || !this.isString(message.timestamp)) {
      this.addError('Message timestamp is required and must be a string');
    } else {
      // Validate timestamp format
      try {
        new Date(message.timestamp);
      } catch {
        this.addError('Invalid timestamp format');
      }
    }

    // Sanitize all string values
    const sanitizedMessage = this.sanitizeObject(message);

    if (this.errors.length > 0) {
      throw new Error('Message validation failed');
    }

    return sanitizedMessage;
  }

  private sanitizeObject(obj: Record<string, unknown>): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(obj)) {
      if (this.isString(value)) {
        sanitized[key] = this.sanitizeString(value);
      } else if (this.isObject(value)) {
        sanitized[key] = this.sanitizeObject(value as Record<string, unknown>);
      } else if (this.isArray(value)) {
        sanitized[key] = value.map(item => 
          this.isString(item) ? this.sanitizeString(item) : 
          this.isObject(item) ? this.sanitizeObject(item as Record<string, unknown>) : 
          item
        );
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private sanitizeString(str: string): string {
    // Remove potential XSS patterns
    let sanitized = str;
    
    XSS_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    // HTML encode special characters
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');

    return sanitized;
  }
}

// ============================================================================
// Utility Functions
// ============================================================================

export function sanitizeHTML(html: string): string {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

export function validateURL(url: string): ValidationResult {
  const validator = new URLValidator();
  return validator.validate(url);
}

export function validateDomain(domain: string): ValidationResult {
  const validator = new DomainValidator();
  return validator.validate(domain);
}

export function validateSessionId(sessionId: string): ValidationResult {
  const validator = new SessionIdValidator();
  return validator.validate(sessionId);
}

export function validateCSSSelector(selector: string): ValidationResult {
  const validator = new CSSelectorValidator();
  return validator.validate(selector);
}

export function validateMessage(message: unknown): ValidationResult & { data?: Record<string, unknown> } {
  const validator = new MessageValidator();
  return validator.validate(message);
}

export function isValidJSON(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

export function sanitizeForLog(data: unknown): unknown {
  if (typeof data === 'string') {
    return sanitizeHTML(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeForLog);
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeForLog(value);
    }
    return sanitized;
  }
  
  return data;
}

// ============================================================================
// Content Security Policy Helpers
// ============================================================================

export function validateCSPCompliance(content: string): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  // Check for inline scripts
  if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(content)) {
    result.errors.push('Inline scripts are not allowed');
    result.isValid = false;
  }

  // Check for inline event handlers
  if (/on\w+\s*=/gi.test(content)) {
    result.errors.push('Inline event handlers are not allowed');
    result.isValid = false;
  }

  // Check for eval usage
  if (/\beval\s*\(/gi.test(content)) {
    result.errors.push('eval() usage is not allowed');
    result.isValid = false;
  }

  return result;
}

// ============================================================================
// Rate Limiting Validator
// ============================================================================

export class RateLimitValidator {
  private requests = new Map<string, number[]>();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }

  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }

  reset(identifier?: string): void {
    if (identifier) {
      this.requests.delete(identifier);
    } else {
      this.requests.clear();
    }
  }
}

// ============================================================================
// Export All Validators
// ============================================================================

export const validators = {
  url: new URLValidator(),
  domain: new DomainValidator(),
  sessionId: new SessionIdValidator(),
  cssSelector: new CSSelectorValidator(),
  message: new MessageValidator(),
};

export const rateLimiter = new RateLimitValidator();