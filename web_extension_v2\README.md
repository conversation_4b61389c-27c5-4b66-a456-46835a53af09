# ScreenMonitorMCP Web Extension Server v2.0

Modern Python server component for browser extension integration with comprehensive security, validation, and clean architecture.

## 🚀 Features

### Core Capabilities
- **Browser Extension Integration**: Seamless communication with browser extensions
- **Session Management**: Secure session handling with automatic cleanup
- **Domain Whitelist**: Configurable domain access control
- **Event Processing**: Real-time DOM event processing and analysis
- **Smart Click API**: AI-powered element interaction endpoints
- **Configuration Management**: Environment-based configuration system

### Security Features
- **Input Validation**: Comprehensive request validation and sanitization
- **Rate Limiting**: Configurable rate limiting per session/IP
- **CSRF Protection**: Token-based CSRF protection
- **XSS Prevention**: Built-in XSS pattern detection and blocking
- **Secure Sessions**: Session timeout and validation
- **Origin Validation**: Strict origin checking for requests

### Architecture Highlights
- **Type Safety**: Full type hints and data validation
- **Clean Architecture**: Modular design with clear separation of concerns
- **Environment Configuration**: Development, production, and test environments
- **Comprehensive Testing**: Unit tests with high coverage
- **Async Support**: Modern async/await patterns

## 📋 Requirements

- Python 3.8+
- FastAPI or Flask (depending on implementation)
- Required Python packages (see requirements.txt)

## 🛠️ Installation

### For Users

```bash
# Install from the main ScreenMonitorMCP project
pip install -e .

# Or install dependencies directly
pip install -r web_extension_v2/requirements.txt
```

### For Developers

```bash
# Clone the repository
git clone <repository-url>
cd web_extension_v2

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install -r requirements-dev.txt
```

## 🏗️ Architecture

### Project Structure

```
web_extension_v2/
├── __init__.py          # Module initialization
├── models.py            # Data models and validation
├── security.py          # Security utilities and validation
├── config.py            # Configuration management
├── server.py            # Main server implementation
├── tests/               # Test suites
│   ├── test_models.py
│   ├── test_security.py
│   └── test_config.py
└── README.md            # This file
```

### Core Components

#### Data Models
Type-safe data models with validation:

```python
from web_extension_v2.models import SessionInfo, DOMEvent, SmartClickRequest

# Create session info
session = SessionInfo(
    session_id="session_123",
    domain="localhost",
    url="http://localhost:3000",
    features=["dom_monitoring", "smart_click"]
)

# Create DOM event
event = DOMEvent(
    event_type=EventType.CLICK,
    url="http://localhost:3000",
    target=ElementInfo(tag_name="BUTTON", id="submit-btn")
)

# Validate and serialize
event_json = event.to_json()
```

#### Security System
Comprehensive security with multiple layers:

```python
from web_extension_v2.security import SecurityManager, InputValidator

# Initialize security manager
security = SecurityManager(SecurityConfig())

# Validate input
is_valid, errors = InputValidator.validate_request_data({
    'domain': 'localhost',
    'session_id': 'session_123',
    'url': 'http://localhost:3000'
})

# Rate limiting
if not security.rate_limiter.is_allowed('user_123'):
    return {'error': 'Rate limit exceeded'}

# CSRF protection
token = security.csrf_protection.generate_token('session_123')
```

#### Configuration Management
Environment-based configuration:

```python
from web_extension_v2.config import get_config, is_feature_enabled

# Get current configuration
config = get_config()

# Check feature status
if is_feature_enabled('web_monitoring'):
    # Feature is enabled
    pass

# Check domain permissions
if is_domain_allowed('example.com'):
    # Domain is allowed
    pass
```

## ⚙️ Configuration

### Environment Variables

```bash
# Environment
export WEB_EXTENSION_ENV=development  # development, production, test

# Server
export WEB_EXTENSION_HOST=localhost
export WEB_EXTENSION_PORT=7777
export WEB_EXTENSION_DEBUG=true

# Extension
export ENABLE_WEB_EXTENSION=true
export ALLOWED_DOMAINS=localhost,127.0.0.1,github.com

# Security
export ENABLE_RATE_LIMITING=true
export RATE_LIMIT_REQUESTS=100
export ENABLE_CSRF_PROTECTION=true

# Logging
export LOG_LEVEL=DEBUG
export LOG_FILE=logs/web_extension.log
```

### Configuration File

```json
{
  "environment": "development",
  "server": {
    "host": "localhost",
    "port": 7777,
    "debug": true
  },
  "security": {
    "enable_rate_limiting": true,
    "rate_limit_requests": 100,
    "allowed_origins": [
      "chrome-extension://",
      "http://localhost"
    ]
  },
  "extension": {
    "enabled": true,
    "allowed_domains": [
      "localhost",
      "127.0.0.1",
      "github.com"
    ]
  }
}
```

### Environment Presets

#### Development
```python
config = get_development_config()
# - Debug mode enabled
# - Rate limiting disabled
# - Extended domain whitelist
# - Detailed logging
```

#### Production
```python
config = get_production_config()
# - Debug mode disabled
# - Rate limiting enabled
# - Minimal domain whitelist
# - Error-level logging only
```

#### Test
```python
config = get_test_config()
# - Test-specific settings
# - Isolated environment
# - Fast timeouts
```

## 🔌 API Endpoints

### Extension Registration

```http
POST /api/extension/register
Content-Type: application/json

{
  "domain": "localhost",
  "features": ["dom_monitoring", "smart_click"],
  "user_agent": "Chrome/91.0",
  "session_id": "session_123"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "session_id": "session_123",
    "enabled_features": ["dom_monitoring", "smart_click"],
    "server_version": "2.0.0"
  }
}
```

### DOM Event Processing

```http
POST /api/extension/dom-event
Content-Type: application/json

{
  "session_id": "session_123",
  "event_type": "click",
  "event_data": {
    "url": "http://localhost:3000",
    "target": {
      "tag_name": "BUTTON",
      "id": "submit-btn"
    },
    "coordinates": {"x": 100, "y": 200}
  }
}
```

### Smart Click

```http
POST /api/extension/smart-click
Content-Type: application/json

{
  "session_id": "session_123",
  "element_description": "Click the submit button",
  "css_selector": "#submit-btn",
  "confidence_threshold": 0.8
}
```

Response:
```json
{
  "success": true,
  "data": {
    "element_found": true,
    "coordinates": {"x": 100, "y": 200},
    "confidence": 0.95
  }
}
```

### Configuration

```http
GET /api/extension/config
```

Response:
```json
{
  "success": true,
  "data": {
    "enabled_features": ["dom_monitoring", "smart_click"],
    "allowed_domains": ["localhost", "127.0.0.1"],
    "server_version": "2.0.0"
  }
}
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
python -m pytest

# Run with coverage
python -m pytest --cov=web_extension_v2

# Run specific test file
python -m pytest tests/test_security.py

# Run with verbose output
python -m pytest -v

# Run tests in parallel
python -m pytest -n auto
```

### Test Structure

```
tests/
├── test_models.py         # Data model tests
├── test_security.py       # Security system tests
├── test_config.py         # Configuration tests
├── test_server.py         # Server endpoint tests
└── conftest.py            # Test fixtures
```

### Writing Tests

```python
import pytest
from web_extension_v2.models import SessionInfo
from web_extension_v2.security import InputValidator

def test_session_creation():
    session = SessionInfo(
        session_id="test_123",
        domain="localhost",
        url="http://localhost:3000"
    )
    
    assert session.session_id == "test_123"
    assert session.domain == "localhost"
    assert session.is_active()

def test_input_validation():
    is_valid, errors = InputValidator.validate_domain("localhost")
    assert is_valid
    assert len(errors) == 0
    
    is_valid, errors = InputValidator.validate_domain("invalid domain")
    assert not is_valid
    assert len(errors) > 0
```

## 🛡️ Security

### Input Validation

All inputs are validated using comprehensive validators:

```python
from web_extension_v2.security import InputValidator

# Domain validation
is_valid, error = InputValidator.validate_domain("example.com")

# URL validation
is_valid, error = InputValidator.validate_url("http://localhost:3000")

# Session ID validation
is_valid, error = InputValidator.validate_session_id("session_123")

# Complete request validation
is_valid, errors = InputValidator.validate_request_data(request_data)
```

### Rate Limiting

```python
from web_extension_v2.security import RateLimiter

limiter = RateLimiter(max_requests=100, window_seconds=60)

if limiter.is_allowed("user_123"):
    # Process request
    pass
else:
    # Rate limit exceeded
    return {"error": "Rate limit exceeded"}
```

### CSRF Protection

```python
from web_extension_v2.security import CSRFProtection

csrf = CSRFProtection()

# Generate token
token = csrf.generate_token("session_123")

# Validate token
if csrf.validate_token("session_123", token):
    # Valid token
    pass
```

### Session Security

```python
from web_extension_v2.security import SessionSecurity

session_mgr = SessionSecurity(timeout_seconds=1800)

# Create session
session_mgr.create_session("session_123", {
    "user_id": 123,
    "domain": "localhost"
})

# Validate session
if session_mgr.is_session_valid("session_123"):
    # Session is valid
    pass

# Update activity
session_mgr.update_activity("session_123")
```

### Security Decorators

```python
from web_extension_v2.security import validate_input, rate_limit, require_valid_session

@validate_input(InputValidator.validate_request_data)
@rate_limit(rate_limiter)
@require_valid_session(session_manager)
async def process_request(request_data, session_id):
    # Secure request processing
    return {"success": True}
```

## 🔧 Development

### Setting Up Development Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements-dev.txt

# Set environment variables
export WEB_EXTENSION_ENV=development
export WEB_EXTENSION_DEBUG=true

# Run tests
python -m pytest

# Start development server
python -m web_extension_v2.server
```

### Code Quality

```bash
# Format code
black web_extension_v2/

# Sort imports
isort web_extension_v2/

# Type checking
mypy web_extension_v2/

# Linting
flake8 web_extension_v2/

# Security scanning
bandit -r web_extension_v2/
```

### Debugging

1. Enable debug mode: `WEB_EXTENSION_DEBUG=true`
2. Set log level: `LOG_LEVEL=DEBUG`
3. Check logs in console or log file
4. Use Python debugger: `import pdb; pdb.set_trace()`

## 📊 Monitoring

### Health Check

```http
GET /api/health
```

Response:
```json
{
  "status": "healthy",
  "version": "2.0.0",
  "uptime": 3600,
  "active_sessions": 5
}
```

### Statistics

```http
GET /api/stats
```

Response:
```json
{
  "sessions": {
    "total": 10,
    "active": 5,
    "expired": 5
  },
  "security": {
    "rate_limit_hits": 2,
    "validation_errors": 1,
    "csrf_tokens": 5
  }
}
```

## 🐛 Troubleshooting

### Common Issues

#### Server Won't Start
1. Check port availability: `netstat -an | grep 7777`
2. Verify Python version: `python --version`
3. Check dependencies: `pip list`

#### Extension Connection Failed
1. Verify server is running
2. Check firewall settings
3. Ensure CORS is configured correctly

#### Rate Limiting Issues
1. Check rate limit configuration
2. Verify identifier uniqueness
3. Consider increasing limits for development

### Debug Mode

Enable comprehensive logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Or via environment
export LOG_LEVEL=DEBUG
```

### Performance Issues

1. Monitor session cleanup intervals
2. Check rate limiter memory usage
3. Profile request processing times
4. Consider caching frequently accessed data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Run code quality checks
7. Submit a pull request

### Code Style

- Follow PEP 8 style guidelines
- Use type hints for all functions
- Write comprehensive docstrings
- Add unit tests for new features
- Use meaningful variable names

## 📄 License

MIT License - see LICENSE file for details.

## 🔗 Related Projects

- [Browser Extension v2](../browser_extension_v2/README.md) - TypeScript browser extension
- [ScreenMonitorMCP Server](../README.md) - Main MCP server

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the test files for usage examples
3. Search existing issues
4. Create a new issue with detailed information