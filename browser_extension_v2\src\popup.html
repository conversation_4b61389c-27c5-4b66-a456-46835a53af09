<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            font-size: 12px;
            color: #666;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>ScreenMonitorMCP</h3>
        <p>Browser Extension</p>
    </div>
    
    <div id="status" class="status active">
        ✅ Extension Active
    </div>
    
    <button id="testBtn">Test Connection</button>
    <button id="settingsBtn">Settings</button>
    
    <div class="info">
        <p><strong>Version:</strong> 2.0.0</p>
        <p><strong>Status:</strong> Ready</p>
        <p>Check console for logs</p>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
